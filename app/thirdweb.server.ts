import { Resource } from "sst";
import { createThirdwebClient } from "thirdweb";
import dotenv from 'dotenv';
import { createAuth } from "thirdweb/auth";
import { privateKeyToAccount } from "thirdweb/wallets";
dotenv.config();

const privateKey = process.env.THIRDWEB_ADMIN_PRIVATE_KEY || Resource.ThirdwebAdminPrivateKey.value;
if (!privateKey) {
  throw new Error("Missing THIRDWEB_ADMIN_PRIVATE_KEY in .env file.");
}
const secretKey = process.env.THIRDWEB_SECRET_KEY || Resource.ThirdwebSecretKey.value;
export const thirdWebClientServer = createThirdwebClient({ secretKey: secretKey || "" });


export const thirdwebAuth = createAuth({
    domain: process.env.THIRDWEB_AUTH_DOMAIN || "readables.ai",
    adminAccount: privateKeyToAccount({ client: thirdWebClientServer, privateKey: privateKey }),
    client: thirdWebClientServer
});
