import { Resource } from "sst";
import { DynamoDBClient, PutItemCommand, QueryCommand } from '@aws-sdk/client-dynamodb';
import { ReadableStatus, type Readable } from "./lib/types";
import { GetObjectCommand, S3Client } from "@aws-sdk/client-s3";

// Helper function to convert stream to string
async function streamToString(stream: any): Promise<string> {
    return new Promise((resolve, reject) => {
        const chunks: Buffer[] = [];
        stream.on('data', (chunk:any) => chunks.push(Buffer.from(chunk)));
        stream.on('error', (err:any) => reject(err));
        stream.on('end', () => resolve(Buffer.concat(chunks).toString('utf8')));
    });
}


const signInOrUp = async (wallet_address: string, chain_id: number): Promise<boolean> => {

    const client = new DynamoDBClient({ region: "eu-central-1" });

    const putCommand = new PutItemCommand({
      TableName: Resource.ReadablesUsers.name,
      Item: {
        userId: { S: wallet_address },
        chainId: { N: chain_id.toString() },
        createdAt: { S: new Date().toISOString() },
      },
    });
    const response = await client.send(putCommand);
    console.log(response);

    client.destroy();

    return true;
}

const getReadableContent = async (wallet_address: string, readable_id: string): Promise<string> => {
    console.log("getReadableContent: ", wallet_address, readable_id);
    if( !wallet_address || wallet_address === "" ) {
        return "";
    }
    if( !readable_id || readable_id === "" ) {
        return "";
    }

    const client = new DynamoDBClient({ region: "eu-central-1" });

    const queryCommand = new QueryCommand({
        ExpressionAttributeValues: {
            ":userId": { S: wallet_address },
            ":readableId": { S: readable_id }
        },
        KeyConditionExpression: "userId = :userId AND readableId = :readableId",
        TableName: Resource.ReadablesReadables.name,
    });
    const response = await client.send(queryCommand);
    console.log(response);
    if( response.Items && response.Items.length > 0 ) {
        console.log("getReadableContent: ", response.Items[0]);

        // get s3_bucket and s3_key_processed from item and then retrieve the content from s3
        const item = response.Items[0];
        const s3_bucket = item.s3_bucket.S || "";
        const s3_key_processed = item.s3_key_processed.S || "";
        if( !s3_bucket || s3_bucket === "" ) {
            return "";
        }
        if( !s3_key_processed || s3_key_processed === "" ) {
            return "";
        }
        const s3 = new S3Client({ region: "eu-central-1" });
        const getObjectCommand = new GetObjectCommand({
            Bucket: s3_bucket,
            Key: s3_key_processed
        });
        const getObjectResponse = await s3.send(getObjectCommand);
        const content = await streamToString(getObjectResponse.Body);
        console.log("getReadableContent: ", content.length);
        return content;
    } else {
        return "";
    }
}

const getReadablesForUser = async (wallet_address: string): Promise<Array<Readable>> => {

    console.log("getReadablesForUser: ", wallet_address);
    if( !wallet_address || wallet_address === "" ) {
        return [];
    }

    const client = new DynamoDBClient({ region: "eu-central-1" });
    const ret:Array<Readable> = [];

    try {
        const queryCommand = new QueryCommand({
            IndexName: "byUserId",
            ExpressionAttributeValues: {
                ":userId": { S: wallet_address }
            },
            KeyConditionExpression: "userId = :userId",
            TableName: Resource.ReadablesReadables.name,
        });
        const response = await client.send(queryCommand);
        console.log(response);
        if( response.Items ) {
            response.Items.forEach((item) => {
                console.log(item);
                ret.push({
                    id: item.readableId.S || "",
                    title: item.title.S || "",
                    status: item.status.S as ReadableStatus || ReadableStatus.PROCESSING,
                    type: item.type.S || "",
                    createdAt: item.createdAt.S || "",
                    tags: item.tags.SS || [],
                    readingTimeInMinutes: item.readingTimeInMinutes.N ? parseFloat(item.readingTimeInMinutes.N) : 0,
                    numberOfWords: item.numberOfWords.N ? parseFloat(item.numberOfWords.N) : 0,
                });
            });
        }
    } catch (err) {
        console.log(err);
    }

    client.destroy();

    return ret;
}

export { signInOrUp, getReadablesForUser, getReadableContent };
