import { S3Client, GetObjectCommand, HeadObjectCommand, PutObjectCommand } from "@aws-sdk/client-s3";
import { Readable } from "stream";
import { DynamoDBClient, PutItemCommand, UpdateItemCommand } from "@aws-sdk/client-dynamodb";
import { LambdaClient, InvokeCommand } from "@aws-sdk/client-lambda";
import { Resource } from "sst";

interface PreprocessedContent {
    normalized: string,
    title: string,
    tags: string[],
    readingTimeInMinutes: number,
    numberOfWords: number,
    type: string
}

interface SQSEvent {
    Records: Record[];
}

interface Record {
    messageId: string;
    receiptHandle: string;
    body: string;
    attributes: Attributes;
    messageAttributes: Attributes;
    md5OfBody: string;
    eventSource: string;
    eventSourceARN: string;
    awsRegion: string;
}

interface Attributes {
    // Empty interface for SQS event attributes
}

// Helper function to call the Python text extractor Lambda
async function callTextExtractorLambda(bucket: string, key: string, fileType: string): Promise<PreprocessedContent> {
    console.log(`Calling TextExtractor Lambda for ${fileType} file: ${key}`);

    const lambdaClient = new LambdaClient({ region: "eu-central-1" });

    // Prepare the request payload
    const payload = {
        bucket: bucket,
        key: key,
        file_type: fileType,
        chunk_size: 1000  // Default chunk size
    };

    try {
        // Invoke the TextExtractor lambda
        const command = new InvokeCommand({
            FunctionName: Resource.ReadablesTextExtractor.name,
            InvocationType: 'RequestResponse',
            Payload: JSON.stringify(payload),
        });

        const response = await lambdaClient.send(command);

        // Parse the response
        if (!response.Payload) {
            throw new Error('Empty response from TextExtractor');
        }

        const responseText = Buffer.from(response.Payload).toString('utf-8');
        const result = JSON.parse(responseText);

        if (result.statusCode !== 200) {
            throw new Error(`TextExtractor Lambda failed: ${JSON.stringify(result)}`);
        }

        // Parse the body
        const body = JSON.parse(result.body);

        // Create the preprocessed content
        const preprocessedContent: PreprocessedContent = {
            normalized: body.chunks.join('\n'),
            title: body.title || 'Readable title',
            tags: ["upload"],  // We could potentially extract tags in the future
            readingTimeInMinutes: body.reading_time.minutes,
            numberOfWords: body.reading_time.estimated_words,
            type: body.file_type
        };

        console.log(`TextExtractor Lambda processed ${body.chunk_count} chunks`);
        console.log(`Estimated reading time: ${body.reading_time.formatted}`);

        return preprocessedContent;
    } catch (error) {
        console.error('Error invoking TextExtractor Lambda:', error);
        throw error;
    } finally {
        lambdaClient.destroy();
    }
}


const createReadableInDynamoDB = async (userId:string, s3_bucket: string, s3_key_original:string): Promise<string>  => {

    console.log("Saving readable to DynamoDB:");

    const client = new DynamoDBClient({ region: "eu-central-1" });

    const readableId = crypto.randomUUID();

    const putCommand = new PutItemCommand({
      TableName: Resource.ReadablesReadables.name,
      Item: {
        readableId: { S: readableId },
        userId: { S: userId },
        createdAt: { S: new Date().toISOString() },
        status: { S: "processing" },
        s3_bucket: { S: s3_bucket },
        s3_key_original: { S: s3_key_original },
        s3_key_processed: { S: "" },
      },
    });
    const response = await client.send(putCommand);
    console.log(response);

    client.destroy();

    return readableId;
}

export const handler = async (event: SQSEvent) => {
    try {
        // Parse the S3 event from the SQS message
        const s3Event = JSON.parse(event.Records[0].body);
        console.log("S3 Event:", s3Event.Records[0].s3);

        const bucket = s3Event.Records[0].s3.bucket.name;
        const key = s3Event.Records[0].s3.object.key;

        // Initialize S3 client
        const s3Client = new S3Client({ region: s3Event.Records[0].awsRegion });

        // Get object metadata
        const headObjectCommand = new HeadObjectCommand({
            Bucket: bucket,
            Key: key
        });

        const headObjectResponse = await s3Client.send(headObjectCommand);
        console.log("Object Metadata:", {
            ContentType: headObjectResponse.ContentType,
            ContentLength: headObjectResponse.ContentLength,
            LastModified: headObjectResponse.LastModified,
            Metadata: headObjectResponse.Metadata
        });

        // Get the object content
        const getObjectCommand = new GetObjectCommand({
            Bucket: bucket,
            Key: key
        });

        const getObjectResponse = await s3Client.send(getObjectCommand);

        // Process the object content based on its type
        if (getObjectResponse.Body instanceof Readable) {
            // Access user_id from metadata
            const userId = headObjectResponse.Metadata?.user_id;
            console.log("User ID from metadata:", userId);

            if( !userId ) {
                throw new Error("User ID not found in metadata");
            }

            const contentType = headObjectResponse.ContentType || '';

            // Save readable to dynamodb with status "processing"
            console.log("Saving readable to DynamoDB with status 'processing'");
            const readableId = await createReadableInDynamoDB(userId, Resource.ReadablesBucket.name, key);

            let preProcessedContent:PreprocessedContent = {
                normalized: "",
                title: "",
                tags: [],
                readingTimeInMinutes: 0,
                numberOfWords: 0,
                type: ""
            };

            // Process content based on content type
            if (contentType.toLowerCase() === 'text/plain') {
                console.log("Processing text/plain content");
                // Call the Python text extractor Lambda for txt files
                preProcessedContent = await callTextExtractorLambda(bucket, key, 'txt');

            } else if (contentType.toLowerCase() === 'application/pdf') {
                console.log("Processing PDF content");
                // Call the Python text extractor Lambda for PDF files
                preProcessedContent = await callTextExtractorLambda(bucket, key, 'pdf');

            } else if (contentType.toLowerCase() === 'application/epub+zip') {
                console.log("Processing EPUB content");
                // Call the Python text extractor Lambda for EPUB files
                preProcessedContent = await callTextExtractorLambda(bucket, key, 'epub');

            } else {
                console.log(`Content type ${contentType} not supported for preprocessing`);
                // Here you could add handlers for other content types:
                // - application/msword
                // - application/vnd.openxmlformats-officedocument.wordprocessingml.document
                throw new Error(`Content type ${contentType} not supported for preprocessing`);
            }

            // Save processed text to s3 with the same key as the original but with a different suffix
            console.log("Saving processed text to S3");
            const putObjectCommand = new PutObjectCommand({
                Bucket: Resource.ReadablesBucket.name,
                Key: key+'_processed',
                Body: preProcessedContent.normalized,
                ContentType: 'text/plain',
                Metadata: headObjectResponse.Metadata,
            });
            await s3Client.send(putObjectCommand);
            console.log("Processed text saved to S3");


            // Save preprocessed content to dynamodb
            console.log("Saving preprocessed content to DynamoDB");
            console.log(preProcessedContent);
            const client = new DynamoDBClient({ region: "eu-central-1" });
            const putCommand = new UpdateItemCommand({
                TableName: Resource.ReadablesReadables.name,
                Key: {
                    userId: { S: userId },
                    readableId: { S: readableId },
                },
                UpdateExpression: "SET #title = :title, #tags = :tags, #s3_key_processed = :s3_key_processed, #status = :status, #readingTimeInMinutes = :readingTimeInMinutes, #numberOfWords = :numberOfWords, #type = :type",
                ExpressionAttributeNames: {
                    "#title": "title",
                    "#tags": "tags",
                    "#s3_key_processed": "s3_key_processed",
                    "#status": "status",
                    "#readingTimeInMinutes": "readingTimeInMinutes",
                    "#numberOfWords": "numberOfWords",
                    "#type": "type",
                },
                ExpressionAttributeValues: {
                    ":title": { S: preProcessedContent.title },
                    ":tags": { SS: preProcessedContent.tags },
                    ":s3_key_processed": { S: key+'_processed' },
                    ":status": { S: "ready" },
                    ":readingTimeInMinutes": { N: preProcessedContent.readingTimeInMinutes.toString() },
                    ":numberOfWords": { N: preProcessedContent.numberOfWords.toString() },
                    ":type": { S: preProcessedContent.type },
                },
            });
            await client.send(putCommand);

            return {
                statusCode: 200,
                body: JSON.stringify({
                    message: "File processed successfully",
                    metadata: headObjectResponse.Metadata,
                    contentType: headObjectResponse.ContentType,
                    contentLength: headObjectResponse.ContentLength,
                    processedContentLength: preProcessedContent.normalized.length
                })
            };
        } else {
            return {
                statusCode: 500,
                body: JSON.stringify({
                    message: "File not processed successfully",
                    metadata: headObjectResponse.Metadata,
                    contentType: headObjectResponse.ContentType,
                    contentLength: headObjectResponse.ContentLength,
                })
            };

        }
    } catch (error: unknown) {
        console.error("Error processing file:", error);
        const errorMessage = error instanceof Error ? error.message : String(error);
        return {
            statusCode: 500,
            body: JSON.stringify({
                message: "Error processing file",
                error: errorMessage
            })
        };
    }
}
