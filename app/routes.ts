import { type RouteConfig, layout, route } from "@react-router/dev/routes";

export default [

    route("/enter", "pages/enter.tsx"),
    route("/landing", "pages/landing.tsx"),

    layout("components/shared/AuthLayout.tsx",[
        route("/","pages/app.tsx"),
        route("/create", "pages/create.tsx"),
        route("/readables", "routes/readables.ts"),
        route("/readables/:id", "routes/readable.ts"),
    ]),

    route("/login", "routes/login.ts"),
    route("/logout", "routes/logout.ts"),
    route("/isloggedin", "routes/isloggedin.ts"),


] satisfies RouteConfig;
