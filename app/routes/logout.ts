import type { Route } from "./+types/logout";
import { destroySession, getSession } from "~/sessions.server";

// POST /logout
export async function action({request}: Route.ActionArgs) {
    const session = await getSession(request.headers.get("Cookie"));
    const destroy = await destroySession(session);

    console.log("destroy: ", destroy);
    return Response.json({}, {
        headers: {
            "Set-Cookie": destroy
        }
    });

}
