import { isSessionAuthed } from "~/sessions.server";
import type { Route } from "./+types/isloggedin";

// GET /isloggedin
export async function loader({request}: Route.LoaderArgs) {

    const isAuthed = await isSessionAuthed(request.headers.get("Cookie")||'');
	if (!isAuthed) {
        console.log("/isloggedin: invalid token");
		return Response.json(false);
	}
    console.log("valid token");
    return Response.json(true);
}
