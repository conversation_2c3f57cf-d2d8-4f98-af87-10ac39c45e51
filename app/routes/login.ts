import type { Route } from "./+types/login";
import { thirdwebAuth } from "~/thirdweb.server";
import { commitSession, getSession } from "~/sessions.server";
import { signInOrUp } from "~/persistence.server";

// GET /login?address=0x123&chainId=501
export async function loader({request}: Route.LoaderArgs) {
    const [,searchParams] = request.url.split("?");
    const address = new URLSearchParams(searchParams).get("address");
    const chainId = new URLSearchParams(searchParams).get("chainId");

    const ret = await thirdwebAuth.generatePayload({
        address: address || "",
        chainId: chainId ? parseInt(chainId) : undefined,
    });

    return Response.json(ret);
}

// POST /login
export async function action({request}: Route.ActionArgs) {
    const session = await getSession(request.headers.get("Cookie"));
    const payload = await request.json();

    console.log("payload: ", payload);

    const verifiedPayload = await thirdwebAuth.verifyPayload(payload);

    if (verifiedPayload.valid) {
        const jwt = await thirdwebAuth.generateJWT({payload: verifiedPayload.payload,});
        session.set("jwt", jwt);
        const cs = await commitSession(session);

        const ret = await signInOrUp(
            verifiedPayload.payload.address,
            verifiedPayload.payload.chain_id ? parseInt(verifiedPayload.payload.chain_id) : 0,
        );
        console.log("ret: ", ret);

        const commit = await commitSession(session);

        return Response.json({"token":jwt}, {
            headers: {
                "Set-Cookie": commit
            }
        });
    } else {
      return Response.error();
    }
  }
