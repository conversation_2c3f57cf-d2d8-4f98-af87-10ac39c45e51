import React from 'react';
import { Link, useLocation, useNavigate } from 'react-router';
import Logo from './Logo';
import { useActiveWallet } from 'thirdweb/react';

const Header: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const activeWallet = useActiveWallet();

  // Function to determine if a link is active
  const isActive = (path: string) => {
    return location.pathname === path;
  };

  // Function to handle logout
  const handleLogout = async () => {
    activeWallet?.disconnect();

    try {
      await fetch('/logout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      // After successful logout, redirect to the entry page
      navigate('/enter');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  return (
    <>
    <div className="w-full mb-12">
      {/* Header content */}
      <div className="relative px-6 py-4 w-full">
        {/* Container for logo and navigation - full width */}
        <div className="w-full flex justify-between items-center">
          {/* Logo */}
          <Logo variant="dark" size="md" />

          {/* Navigation - Centered horizontally */}
          <div className="flex-1 flex justify-center">
            <nav className="flex space-x-10 text-lg">
              <Link
                to="/"
                className={`${isActive('/')
                  ? 'text-readable-darker-purple font-bold border-b-2 border-readable-purple'
                  : 'text-black'}`}
              >
                Home
              </Link>
              <Link
                to="/create"
                className={`${isActive('/create')
                  ? 'text-readable-darker-purple font-bold border-b-2 border-readable-purple'
                  : 'text-black font-medium'}`}
              >
                Create
              </Link>
              <Link
                to="/library"
                className={`${isActive('/library')
                  ? 'text-readable-darker-purple font-bold border-b-2 border-readable-purple'
                  : 'text-black font-medium'}`}
              >
                Your Library
              </Link>
            </nav>
          </div>

          {/* Logout button */}
          <button
            onClick={handleLogout}
            className="bg-readable-darker-purple text-white px-4 py-2 rounded-3xl font-medium hover:bg-readable-dark-purple transition-colors cursor-pointer"
          >
            Log out
          </button>
        </div>
      </div>
    </div>
    </>
  );
};

export default Header;
