import React from 'react';
import { Link } from 'react-router';
import Logo from './Logo';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="z-40 pt-24 pb-2 bg-footer-pattern bg-transparent">
      <div className="max-w mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-8 gap-8 mb-10">
          {/* Column 1 - logo  */}
          <div className="col-span-6">
            <Logo variant="light" size="md" withText={true} />
          </div>

          {/* Column 2 - Quick links */}
          <div className="col-span-1">
            <h3 className="text-lg font-medium text-white mb-4">Quick links</h3>
            <ul className="space-y-3">
              <li>
                <Link to="/" className="text-white hover:text-gray-900">Home</Link>
              </li>
              <li>
                <Link to="/create" className="text-white hover:text-gray-900">Create</Link>
              </li>
              <li>
                <Link to="/library" className="text-white hover:text-gray-900">Your Library</Link>
              </li>
            </ul>
          </div>

          {/* Column 3 - Socials */}
          <div className="col-span-1">
            <h3 className="text-lg font-medium text-white mb-4">Socials</h3>
            <div className='flex flex-row gap-6'>
                <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M18.9 0H22.581L14.541 10.2071L24 24.0969H16.5945L10.794 15.6713L4.1565 24.0969H0.474L9.0735 13.1785L0 0H7.5945L12.837 7.69937L18.9 0ZM17.61 21.6506H19.65L6.4845 2.31828H4.2975L17.61 21.6506Z" fill="white"/>
                </svg>
                <svg width="30" height="30" viewBox="2 3 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9.75 2.5H20.25C24.25 2.5 27.5 5.75 27.5 9.75V20.25C27.5 22.1728 26.7362 24.0169 25.3765 25.3765C24.0169 26.7362 22.1728 27.5 20.25 27.5H9.75C5.75 27.5 2.5 24.25 2.5 20.25V9.75C2.5 7.82718 3.26384 5.98311 4.62348 4.62348C5.98311 3.26384 7.82718 2.5 9.75 2.5ZM9.5 5C8.30653 5 7.16193 5.47411 6.31802 6.31802C5.47411 7.16193 5 8.30653 5 9.5V20.5C5 22.9875 7.0125 25 9.5 25H20.5C21.6935 25 22.8381 24.5259 23.682 23.682C24.5259 22.8381 25 21.6935 25 20.5V9.5C25 7.0125 22.9875 5 20.5 5H9.5ZM21.5625 6.875C21.9769 6.875 22.3743 7.03962 22.6674 7.33265C22.9604 7.62567 23.125 8.0231 23.125 8.4375C23.125 8.8519 22.9604 9.24933 22.6674 9.54235C22.3743 9.83538 21.9769 10 21.5625 10C21.1481 10 20.7507 9.83538 20.4576 9.54235C20.1646 9.24933 20 8.8519 20 8.4375C20 8.0231 20.1646 7.62567 20.4576 7.33265C20.7507 7.03962 21.1481 6.875 21.5625 6.875ZM15 8.75C16.6576 8.75 18.2473 9.40848 19.4194 10.5806C20.5915 11.7527 21.25 13.3424 21.25 15C21.25 16.6576 20.5915 18.2473 19.4194 19.4194C18.2473 20.5915 16.6576 21.25 15 21.25C13.3424 21.25 11.7527 20.5915 10.5806 19.4194C9.40848 18.2473 8.75 16.6576 8.75 15C8.75 13.3424 9.40848 11.7527 10.5806 10.5806C11.7527 9.40848 13.3424 8.75 15 8.75ZM15 11.25C14.0054 11.25 13.0516 11.6451 12.3483 12.3483C11.6451 13.0516 11.25 14.0054 11.25 15C11.25 15.9946 11.6451 16.9484 12.3483 17.6517C13.0516 18.3549 14.0054 18.75 15 18.75C15.9946 18.75 16.9484 18.3549 17.6517 17.6517C18.3549 16.9484 18.75 15.9946 18.75 15C18.75 14.0054 18.3549 13.0516 17.6517 12.3483C16.9484 11.6451 15.9946 11.25 15 11.25Z" fill="white"/>
                </svg>
                <svg className='h-7 w-7' role="img" viewBox="4 4 24 24" fill='white' strokeWidth={0} stroke='#ffffff' xmlns="http://www.w3.org/2000/svg"><path d="M23.8182 3.95264H7.18156C6.32586 3.95502 5.5059 4.296 4.90083 4.90107C4.29576 5.50614 3.95478 6.32611 3.95239 7.1818V23.8185C3.95478 24.6742 4.29576 25.4941 4.90083 26.0992C5.5059 26.7043 6.32586 27.0453 7.18156 27.0476H23.8182C24.6747 27.0476 25.496 26.7074 26.1016 26.1018C26.7072 25.4963 27.0474 24.6749 27.0474 23.8185V7.1818C27.0474 6.32538 26.7072 5.50402 26.1016 4.89844C25.496 4.29285 24.6747 3.95264 23.8182 3.95264ZM25.7557 23.8185C25.7523 24.3313 25.5471 24.8221 25.1845 25.1847C24.8219 25.5474 24.331 25.7526 23.8182 25.756H7.18156C6.66875 25.7526 6.1779 25.5474 5.81528 25.1847C5.45266 24.8221 5.24744 24.3313 5.24406 23.8185V7.1818C5.24744 6.66899 5.45266 6.17815 5.81528 5.81552C6.1779 5.4529 6.66875 5.24769 7.18156 5.2443H23.8182C24.331 5.24769 24.8219 5.4529 25.1845 5.81552C25.5471 6.17815 25.7523 6.66899 25.7557 7.1818V23.8185Z" fill="white"/>
                    <path d="M8.2356 13.883C8.2356 13.5404 8.37168 13.2119 8.61392 12.9696C8.85615 12.7274 9.18469 12.5913 9.52726 12.5913C9.86983 12.5913 10.1984 12.7274 10.4406 12.9696C10.6828 13.2119 10.8189 13.5404 10.8189 13.883V22.2788C10.8189 22.6214 10.6828 22.9499 10.4406 23.1922C10.1984 23.4344 9.86983 23.5705 9.52726 23.5705C9.18469 23.5705 8.85615 23.4344 8.61392 23.1922C8.37168 22.9499 8.2356 22.6214 8.2356 22.2788V13.883Z" fill="white"/>
                    <path d="M9.52726 10.0028C10.2406 10.0028 10.8189 9.42447 10.8189 8.7111C10.8189 7.99773 10.2406 7.41943 9.52726 7.41943C8.81389 7.41943 8.2356 7.99773 8.2356 8.7111C8.2356 9.42447 8.81389 10.0028 9.52726 10.0028Z" fill="white"/>
                    <path d="M22.7592 17.2695V22.2812C22.7592 22.6238 22.6231 22.9523 22.3808 23.1946C22.1386 23.4368 21.8101 23.5729 21.4675 23.5729C21.1249 23.5729 20.7964 23.4368 20.5541 23.1946C20.3119 22.9523 20.1758 22.6238 20.1758 22.2812V17.2695C20.1758 16.7163 19.956 16.1857 19.5648 15.7945C19.1736 15.4033 18.643 15.1835 18.0898 15.1835C17.5365 15.1835 17.0059 15.4033 16.6147 15.7945C16.2235 16.1857 16.0037 16.7163 16.0037 17.2695V22.2812C16.0037 22.6238 15.8677 22.9523 15.6254 23.1946C15.3832 23.4368 15.0546 23.5729 14.7121 23.5729C14.3695 23.5729 14.041 23.4368 13.7987 23.1946C13.5565 22.9523 13.4204 22.6238 13.4204 22.2812V13.8854C13.4257 13.5445 13.5635 13.219 13.8046 12.9779C14.0457 12.7368 14.3712 12.599 14.7121 12.5937C14.9309 12.5877 15.1472 12.641 15.3382 12.7478C15.5292 12.8547 15.6877 13.0112 15.7971 13.2008C16.5073 12.7983 17.3108 12.5892 18.1272 12.5947C18.9435 12.6001 19.7442 12.8197 20.449 13.2316C21.1539 13.6436 21.7382 14.2333 22.1436 14.9419C22.549 15.6505 22.7613 16.4532 22.7592 17.2695Z" fill="white"/>
                </svg>
            </div>
          </div>
        </div>

        {/* Divider */}
        <div className="border-t border-white my-4"></div>

        {/* Bottom section */}
        <div className="grid grid-cols-1 md:grid-cols-8 gap-8 mb-10">
          <div className="col-span-6">
            <p className="text-sm text-white">Copyright © {currentYear} megahash GmbH</p>
          </div>
          <div className="flex-col space-x-6">
            <Link to="/privacy" className="text-sm text-white hover:text-gray-900">Privacy policy</Link>
          </div>
          <div className="flex-col space-x-6">
            <Link to="/terms" className="text-sm text-white hover:text-gray-900">Terms & conditions</Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
