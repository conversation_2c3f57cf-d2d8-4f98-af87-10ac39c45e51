import React, { useState, useEffect, useRef } from 'react';
import dayjs from 'dayjs';
import { type Readable } from '~/lib/types';
import { File, MoreHorizontal, Upload } from 'lucide-react';

interface ReadableItemProps {
  readable: Readable;
  onClick: (readable: Readable) => void;
}

const ReadableItem: React.FC<ReadableItemProps> = ({ readable, onClick }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  const toggleMenu = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsMenuOpen(!isMenuOpen);
  };

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsMenuOpen(false);
      }
    };

    if (isMenuOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isMenuOpen]);

  const handleMenuAction = (action: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setIsMenuOpen(false);

    switch (action) {
      case 'favorite':
        // TODO: Implement favorite functionality
        console.log('Favorite clicked for:', readable.title);
        break;
      case 'edit':
        // TODO: Implement edit functionality
        console.log('Edit clicked for:', readable.title);
        break;
      case 'delete':
        // TODO: Implement delete functionality
        console.log('Delete clicked for:', readable.title);
        break;
    }
  };

  // Function to render tags
  const renderTags = (tags: string[]) => {
    return tags.map((tag, index) => {
      const tagColorClass =
        tag === "Technology" ? "bg-pink-100 text-pink-800" :
        tag === "Science" ? "bg-green-100 text-green-800" :
        tag === "Article" ? "bg-blue-100 text-blue-800" :
        "bg-gray-100 text-gray-800";

      return (
        <span key={index} className={`${tagColorClass} text-xs px-2 py-1 rounded-full mr-1`}>
          {tag}
        </span>
      );
    });
  };

  return (
    <>
    <div
      className="grid grid-cols-3 gap-4 p-4 border-t border-gray-200 hover:bg-gray-50 justify-items-center"
    >
      {/* Column 1: Title, upload time, type */}
      <div className="flex flex-col">
        <div className="flex items-start align-top" onClick={() => onClick(readable)}>
          <h3 className="text-xl font-medium">{readable.title}</h3>
        </div>
        <div className="flex items-center text-sm text-gray-500 mt-4">
            <Upload size={12} color={'#5D48E5'} className='mr-2' /><span>Uploaded {dayjs(readable.createdAt).fromNow()}</span>
        </div>
        <div className="flex items-center text-sm text-gray-500 mt-2">
            <File size={12} color={'#5D48E5'} className='mr-2' /><span>{readable.type || "Uploaded document"}</span>
        </div>
      </div>

      {/* Column 2: Reading time */}
      <div className="flex flex-col text-center align-top">
        <div className="text-sm text-gray-500">
          Approx. {dayjs(readable.readingTimeInMinutes).format("m")} minutes
        </div>
      </div>

      {/* Column 3: Tags */}
      <div className="flex flex-col align-top ">
        {renderTags(readable.tags)}
      </div>
    </div>
    <div className="relative" ref={menuRef}>
        <button
        className="ml-2 text-gray-400 hover:text-gray-600"
        onClick={toggleMenu}
        >
        <MoreHorizontal size={20} />
        </button>

        {/* Animated submenu */}
        <div
          className={`absolute top-8 left-0 bg-white border border-gray-200 rounded-lg shadow-lg z-10 transition-all duration-200 ease-in-out ${
            isMenuOpen
              ? 'opacity-100 scale-100 translate-y-0'
              : 'opacity-0 scale-95 -translate-y-2 pointer-events-none'
          }`}
        >
          <div className="flex items-center py-2 px-1">
            <button
              onClick={(e) => handleMenuAction('favorite', e)}
              className="px-3 py-2 text-sm hover:bg-gray-50 rounded transition-colors"
              style={{ color: '#9b87f5' }}
            >
              Favorite
            </button>
            <button
              onClick={(e) => handleMenuAction('edit', e)}
              className="px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded transition-colors"
            >
              Edit
            </button>
            <button
              onClick={(e) => handleMenuAction('delete', e)}
              className="px-3 py-2 text-sm text-red-600 hover:bg-gray-50 rounded transition-colors"
            >
              Delete
            </button>
          </div>
        </div>
    </div>
    </>
  );
};

export default ReadableItem;
