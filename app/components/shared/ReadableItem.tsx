import React from 'react';
import dayjs from 'dayjs';
import { type Readable } from '~/lib/types';
import { File, MoreHorizontal, Upload } from 'lucide-react';

interface ReadableItemProps {
  readable: Readable;
  onClick: (readable: Readable) => void;
}

const ReadableItem: React.FC<ReadableItemProps> = ({ readable, onClick }) => {
  // Function to render tags
  const renderTags = (tags: string[]) => {
    return tags.map((tag, index) => {
      const tagColorClass =
        tag === "Technology" ? "bg-pink-100 text-pink-800" :
        tag === "Science" ? "bg-green-100 text-green-800" :
        tag === "Article" ? "bg-blue-100 text-blue-800" :
        "bg-gray-100 text-gray-800";

      return (
        <span key={index} className={`${tagColorClass} text-xs px-2 py-1 rounded-full mr-1`}>
          {tag}
        </span>
      );
    });
  };

  return (
    <>
    <div
      className="grid grid-cols-3 gap-4 p-4 border-t border-gray-200 hover:bg-gray-50 justify-items-center"
    >
      {/* Column 1: Title, upload time, type, and menu button */}
      <div className="flex flex-col">
        <div className="flex items-start align-top" onClick={() => onClick(readable)}>
          <h3 className="text-xl font-medium">{readable.title}</h3>
        </div>
        <div className="flex items-center text-sm text-gray-500 mt-4">
            <Upload size={12} color={'#5D48E5'} className='mr-2' /><span>Uploaded {dayjs(readable.createdAt).fromNow()}</span>
        </div>
        <div className="flex items-center text-sm text-gray-500 mt-2">
            <File size={12} color={'#5D48E5'} className='mr-2' /><span>{readable.type || "Uploaded document"}</span>
        </div>
      </div>

      {/* Column 2: Reading time */}
      <div className="flex flex-col text-center align-top">
        <div className="text-sm text-gray-500">
          Approx. {dayjs(readable.readingTimeInMinutes).format("m")} minutes
        </div>
      </div>

      {/* Column 3: Tags */}
      <div className="flex flex-col align-top ">
        {renderTags(readable.tags)}
      </div>
    </div>
    <div>
        <button
        className="ml-2 text-gray-400 hover:text-gray-600"
        onClick={(e) => {
            e.stopPropagation();
            // Add menu functionality here
        }}
        >
        <MoreHorizontal size={20} />
        </button>
    </div>
    </>
  );
};

export default ReadableItem;
