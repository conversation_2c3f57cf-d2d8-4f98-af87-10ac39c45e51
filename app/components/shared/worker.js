import { KokoroTTS, TextSplitterStream } from "kokoro-js";
import { detectWebGPU, isMobile } from "./utils.js";

let tts = null;

(async () => {
    // Device detection
    const device = (await detectWebGPU()) ? "webgpu" : "wasm";
    const dtype = device === "wasm" ? "q8" : "fp32";

    self.postMessage({ status: "device", device: device, dtype: dtype});

    // Load the model
    const model_id = "onnx-community/Kokoro-82M-v1.0-ONNX";
    tts = await KokoroTTS.from_pretrained(model_id, {
        dtype: dtype,
        device,
        progress_callback: (progress) => {
            self.postMessage({ status: "progress", progress });
        }
    }).catch((e) => {
        self.postMessage({ status: "error", error: e.message });
        throw e;
    });
    self.postMessage({ status: "ready", voices: tts.voices, device });
})();


// Listen for messages from the main thread
self.addEventListener("message", async (e) => {

    if( e.data.type === "init" ) {
        await init();
    }
    if( e.data.type === "generate" ) {
        tts.generate(e.data.text, { voice: e.data.voice }).then((audio) => {
            self.postMessage({ status: "complete", audio: URL.createObjectURL(audio.toBlob()), text: e.data.text });
        });
    }
    if( e.data.type === "stream" ) {

        const { text, voice } = e.data;

        // First, set up the stream
        try {
            const splitter = new TextSplitterStream();
            const stream = tts.stream(splitter, { voice, speed:1.0 });
            (async () => {
                let i = 0;
                for await (const { text, phonemes, audio } of stream) {
                    console.log({ text, phonemes });
                    const blob = audio.toBlob();
                    self.postMessage({ status: "complete", audio: URL.createObjectURL(blob), text });
                }
            })();

            splitter.push(text);
            await new Promise((resolve) => setTimeout(resolve, 10));

            // Finally, close the stream to signal that no more text will be added.
            splitter.close();
        } catch (e) {
            self.postMessage({ status: "error", error: e.message });
        }
    }
});
