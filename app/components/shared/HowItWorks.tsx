import React from 'react';

interface FeatureProps {
  icon: string;
  title: string;
  description: string;
}

const Feature: React.FC<FeatureProps> = ({ icon, title, description }) => {
  return (
    <div className="flex flex-col items-center">
      <div className="bg-[#5D48E5] rounded-full w-12 h-12 flex items-center justify-center mb-4">
        <img src={icon} alt={title} className="w-6 h-6" />
      </div>
      <h3 className="text-xl font-semibold mb-2 text-center">{title}</h3>
      <p className="text-gray-600 text-center text-sm max-w-md">{description}</p>
    </div>
  );
};

const HowItWorks: React.FC = () => {
  return (
    <div className="py-16 ">
      <div className="container mx-auto px-4">
        <h2 className="text-2xl font-bold mb-12 text-center">How it works</h2>
        <div className="grid grid-cols-1 gap-24 relative max-w-md mx-auto">

          {/* Feature 1 */}
          <div className="relative">
            <Feature
              icon="/icon_create.svg"
              title="Create"
              description="Upload documents, paste text, or add website content to create your readable"
            />
            {/* Vertical Connector Line 1 */}
            <div className="absolute left-1/2 -bottom-16 w-0.5 h-10 bg-gray-800 transform -translate-x-1/2" />
          </div>

          {/* Feature 2 */}
          <div className="relative">
            <Feature
              icon="/icon_personalize.svg"
              title="Personalize"
              description="Choose from natural-sounding voices and adjust playback settings"
            />
            {/* Vertical Connector Line 2 */}
            <div className="absolute left-1/2 -bottom-16 w-0.5 h-10 bg-gray-800 transform -translate-x-1/2" />
          </div>

          {/* Feature 3 */}
          <Feature
            icon="/icon_listen.svg"
            title="Listen"
            description="Enjoy your content read aloud and interact with it through voice commands"
          />
        </div>
      </div>
    </div>
  );
};

export default HowItWorks;
