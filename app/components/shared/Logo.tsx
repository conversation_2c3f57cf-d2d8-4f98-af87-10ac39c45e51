import { Link } from "react-router";


type LogoSize = "sm" | "md" | "lg";

interface LogoProps {
  size?: LogoSize;
  withText?: boolean;
  variant?: "light" | "dark";
}

const Logo = ({ size = "md", withText = true, variant = "light" }: LogoProps) => {
  const textSizeClasses = {
    sm: "text-lg",
    md: "text-xl",
    lg: "text-3xl"
  };

  const logoSrc = variant === "light" ? "logo_white.svg" : "Logo_dark_Readables_svg.svg";
  const textColor = variant === "light" ? "text-white" : "readable-darkest-purple";

  return (
    <Link to="/" className="flex items-center">
      <img src={logoSrc} alt="Readables Logo" width={"64px"} />
      {withText && (
        <div className={`${textSizeClasses[size]} font-bold ${textColor}`}>
          readables
        </div>
      )}
    </Link>
  );
};

export default Logo;
