import type { Route } from "./+types/landing";
import { But<PERSON> } from "~/components/ui/button";
import Logo from "~/components/shared/Logo";
import { useNavigate } from "react-router";
import HowItWorks from "~/components/shared/HowItWorks";

export function meta({}: Route.MetaArgs) {
  return [
    { title: "New React Router App" },
    { name: "description", content: "Welcome to React Router!" },
  ];
}

export default function Home() {
  let navigate = useNavigate();

  return (
  <div className="min-h-screen flex flex-col bg-waves-01-pattern">
  <div className="flex-1">
    <div className="px-4 sm:px-6 lg:px-8 py-8 md:py-8">
      <div className="md:flex md:items-center md:justify-between">
        <div className="md:w-1/2 space-y-8">
          <Logo size="md" />

          <h1 className="text-3xl sm:text-3xl font-bold text-white mt-6">
            Reading was yesterday.
          </h1>

          <p className="text-lg text-white font-bold mt-4">
            Let our AI read any text to you. Have a question about the content? Enter interactive mode and discuss with the AI.
          </p>

          <div className="pt-4 flex flex-wrap gap-4">
            <Button
              onClick={() => navigate("/enter")}
              className="bg-white text-readable-purple px-8 py-4 text-md rounded-full"
            >
              Get started
            </Button>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div className="flex-1 bg-transparent mt-24">
    <HowItWorks />
  </div>
</div>);
;
}
