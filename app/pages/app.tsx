import { Link } from "react-router";
import type { Route } from "./+types/app";
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime';
import { useState } from 'react';
import Player from '~/components/shared/Player';
import ReadableItem from '~/components/shared/ReadableItem';
import { ReadableStatus, type Readable } from "~/lib/types";

dayjs.extend(relativeTime)

export function meta({}: Route.MetaArgs) {
  return [
    { title: "readables.ai" },
    { name: "description", content: "Sign in/up to get started." },
  ];
}
export async function clientLoader({ params }: Route.LoaderArgs) {
    const readables = await fetch("/readables");
    const normalized_readables =  await readables.json();
    console.log("normalized_readables: ", normalized_readables);
    return { readables: normalized_readables };
  }

export default function App({loaderData,}: Route.ComponentProps) {
    const { readables  } = loaderData;
    const [playerVisible, setPlayerVisible] = useState(false);
    const [selectedReadable, setSelectedReadable] = useState<Readable>({title: "", status: ReadableStatus.PROCESSING, type: "", createdAt: "", tags: [], id: "", readingTimeInMinutes: 0, numberOfWords: 0 });

  // Sample data for suggested readables
  const suggestedReadables = [
    {
      title: "The Mitochondria is the powerhouse of the cell",
      type: "Uploaded document",
      tags: ["Technology", "Science"]
    }
  ];

  // Function to handle readable click
  const handleReadableClick = (readable: any) => {
    setSelectedReadable(readable);
    setPlayerVisible(true);
  };

  return (
    <div className="flex flex-col h-full">
      {/* Player component */}
      <Player
        isVisible={playerVisible}
        title={selectedReadable.title}
        readable={selectedReadable}
        onClose={() => setPlayerVisible(false)}
      />

      {/* Main content */}
      <div className="flex-1 px-12 py-8 flex flex-col">

        {/* Your recent Readables section */}
        <section className="mb-12">
          <h2 className="text-3xl font-medium mb-4">Your recent Readables</h2>

          {readables && readables.length > 0 ? (
            <div className="w-full">
              {readables.map((readable:any, index:number) => (
                <ReadableItem
                  key={index}
                  readable={readable}
                  onClick={handleReadableClick}
                />
              ))}
            </div>
          ) : (
            <div className="text-md mb-4">You have no Readables yet. Start by <Link className="text-readable-purple" to="/create">creating one</Link>.</div>
          )}
        </section>

        {/* Try one of ours section */}
        <section>
          <h2 className="text-2xl font-bold mb-4">Try one of ours</h2>

          <div className="w-full">
            {suggestedReadables.map((readable, index) => (
              <ReadableItem
                key={index}
                readable={readable as Readable}
                onClick={handleReadableClick}
              />
            ))}
          </div>
        </section>
      </div>
    </div>
  )
}
