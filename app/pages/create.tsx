import { useState, useRef, useEffect } from "react";
import type { Route } from "./+types/create";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
import Header from "~/components/shared/Header";
import { Button } from "~/components/ui/button";
import { Resource } from "sst";
import { getLoggedInUserId } from "~/sessions.server";
import { useToast } from "~/components/ui/toast";
import Spinner from "~/components/ui/spinner";
import { redirect } from "react-router";

export function meta({}: Route.MetaArgs) {
  return [
    { title: "Create - readables.ai" },
    { name: "description", content: "Create a new readable from URL, document, or text." },
  ];
}

// Tab options for the create component
type CreateTab = "url" | "document" | "text";

export async function loader({request}: Route.LoaderArgs) {
    const user_id:string = await getLoggedInUserId(request.headers.get("cookie")||'');
    console.log("user_id: ", user_id);

    if (!user_id || user_id === "") {
        // Redirect to login if session is invalid or expired
        return redirect("/enter");
    }

    const command = new PutObjectCommand({
      Key: crypto.randomUUID(),
      Bucket: Resource.ReadablesUploadBucket.name,
      Metadata: {
        "user_id": user_id,
      },
    });
    const url = await getSignedUrl(new S3Client({}), command);

    return { url };
}

export default function Create({ loaderData }: Route.ComponentProps) {
  const { url } = loaderData;
  const [activeTab, setActiveTab] = useState<CreateTab>("url");
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [pastedText, setPastedText] = useState<string>("");
  const fileInputRef = useRef<HTMLInputElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const { showToast } = useToast();

  return (
    <>
      {/* Main content */}
      <div className="flex-1 px-6 py-8 flex flex-col relative">
        <div className="max-w-5xl mx-auto w-full">
          <h1 className="text-2xl font-bold mb-8">Start creating your next readable</h1>

          {/* Tab selector */}
          <div className="mb-6">
            <div className="bg-gray-100 p-1 rounded-full inline-flex">
              <button
                className={`px-6 py-2 rounded-full text-sm font-medium ${activeTab === "url" ? "bg-[#5D48E5] text-white" : "text-gray-700"}`}
                onClick={() => setActiveTab("url")}
              >
                URL
              </button>
              <button
                className={`px-6 py-2 rounded-full text-sm font-medium ${activeTab === "document" ? "bg-[#5D48E5] text-white" : "text-gray-700"}`}
                onClick={() => setActiveTab("document")}
              >
                Document
              </button>
              <button
                className={`px-6 py-2 rounded-full text-sm font-medium ${activeTab === "text" ? "bg-[#5D48E5] text-white" : "text-gray-700"}`}
                onClick={() => setActiveTab("text")}
              >
                Text
              </button>
            </div>
          </div>

          {/* Content based on active tab */}
          <div className="bg-white rounded-xl shadow-md p-8">
            {activeTab === "url" && (
              <div>
                <div className="mb-6">
                  <input
                    type="text"
                    placeholder="Paste the URL here"
                    className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#5D48E5]"
                  />
                </div>
                <Button className="bg-[#5D48E5] hover:bg-[#4938B8] text-white rounded-full px-8 py-2 font-medium">
                  Start processing
                </Button>
              </div>
            )}

            {activeTab === "document" && (
                <form
                onSubmit={async (e) => {
                  e.preventDefault();
                  if (!selectedFile) return;

                  try {
                    setIsUploading(true);

                    const response = await fetch(url, {
                      body: selectedFile,
                      method: "PUT",
                      headers: {
                        "Content-Type": selectedFile.type,
                        "Content-Disposition": `attachment; filename="${selectedFile.name}"`,
                      },
                    });

                    if (response.ok) {
                        showToast({
                            message: "File uploaded successfully!",
                            type: "success",
                            duration: 5000,
                          });
                     } else {
                        showToast({
                            message: "Failed to upload file. Please try again.",
                            type: "error",
                            duration: 5000,
                          });
                     }

                    // Reset form
                    setSelectedFile(null);
                    if (fileInputRef.current) {
                      fileInputRef.current.value = "";
                    }
                  } catch (error) {
                    console.error("Upload error:", error);
                    showToast({
                      message: "Failed to upload file. Please try again.",
                      type: "error",
                      duration: 5000,
                    });
                  } finally {
                    setIsUploading(false);
                  }
                }}
              >
              <div className="text-center">
                <div className={`mb-6 border-2 border-dashed border-gray-300 rounded-lg p-8 ${isUploading ? 'opacity-50 pointer-events-none' : ''}`}>
                  <div className="flex justify-center mb-4">
                    <div className="bg-gray-100 p-4 rounded-lg">
                      <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M18.6667 2.66675H8.00001C6.52725 2.66675 5.33334 3.86066 5.33334 5.33341V26.6667C5.33334 28.1395 6.52725 29.3334 8.00001 29.3334H24C25.4728 29.3334 26.6667 28.1395 26.6667 26.6667V10.6667L18.6667 2.66675Z" stroke="#5D48E5" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M18.6667 2.66675V10.6667H26.6667" stroke="#5D48E5" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </div>
                  </div>
                  {selectedFile ? (
                    <div className="mb-2">
                      <p className="text-sm font-medium text-gray-700">{selectedFile.name}</p>
                      <p className="text-xs text-gray-500">{(selectedFile.size / 1024).toFixed(1)} KB</p>
                    </div>
                  ) : (
                    <>
                      <p className="text-sm text-gray-600 mb-2">Drag & drop</p>
                      <p className="text-xs text-gray-500">Supports PDF, DOC, DOCX, and TXT files</p>
                    </>
                  )}
                  <div className="mt-4">
                    <label
                      htmlFor="file-upload"
                      className={`cursor-pointer bg-gray-100 text-gray-700 px-6 py-2 rounded-full text-sm font-medium ${isUploading ? 'opacity-50 cursor-not-allowed' : ''}`}
                    >
                      {selectedFile ? "Change file" : "Browse files"}
                      <input
                        id="file-upload"
                        name="file"
                        type="file"
                        className="hidden"
                        accept=".pdf,.doc,.docx,.txt"
                        ref={fileInputRef}
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file) {
                            setSelectedFile(file);
                          }
                        }}
                        disabled={isUploading}
                      />
                    </label>
                    {selectedFile && (
                      <button
                        type="button"
                        className="ml-2 text-sm text-red-600 hover:text-red-800"
                        onClick={() => {
                          setSelectedFile(null);
                          if (fileInputRef.current) {
                            fileInputRef.current.value = "";
                          }
                        }}
                        disabled={isUploading}
                      >
                        Remove
                      </button>
                    )}
                  </div>
                </div>
                <Button
                  type="submit"
                  className="bg-[#5D48E5] hover:bg-[#4938B8] text-white rounded-full px-8 py-2 font-medium"
                  disabled={!selectedFile || isUploading}
                >
                  {isUploading ? (
                    <span className="flex items-center">
                      <Spinner size="sm" className="mr-2 text-white" />
                      Uploading...
                    </span>
                  ) : (
                    "Start processing"
                  )}
                </Button>
              </div>
              </form>
            )}

            {activeTab === "text" && (
              <form
                onSubmit={async (e) => {
                  e.preventDefault();

                  if (!pastedText || pastedText.trim() === '') {
                    showToast({
                      message: "Please enter some text to process.",
                      type: "error",
                      duration: 5000,
                    });
                    return;
                  }

                  try {
                    setIsUploading(true);

                    // Create a Blob from the text
                    const blob = new Blob([pastedText], { type: 'text/plain' });

                    // Create a File object from the Blob
                    const file = new File([blob], `pasted_text_${new Date().toISOString().slice(0, 10)}.txt`, {
                      type: 'text/plain',
                      lastModified: new Date().getTime()
                    });

                    // Upload the file using the same method as document upload
                    const response = await fetch(url, {
                      body: file,
                      method: "PUT",
                      headers: {
                        "Content-Type": file.type,
                        "Content-Disposition": `attachment; filename="${file.name}"`,
                      },
                    });

                    if (response.ok) {
                      showToast({
                        message: "Text uploaded successfully!",
                        type: "success",
                        duration: 5000,
                      });

                      // Clear the textarea
                      setPastedText('');
                    } else {
                      showToast({
                        message: "Failed to upload text. Please try again.",
                        type: "error",
                        duration: 5000,
                      });
                    }
                  } catch (error) {
                    console.error("Upload error:", error);
                    showToast({
                      message: "Failed to upload text. Please try again.",
                      type: "error",
                      duration: 5000,
                    });
                  } finally {
                    setIsUploading(false);
                  }
                }}
              >
                <div>
                  <div className="mb-6">
                    <textarea
                      id="text-input"
                      ref={textareaRef}
                      placeholder="Paste or type your text here..."
                      className="w-full p-3 border border-gray-300 rounded-lg h-64 focus:outline-none focus:ring-2 focus:ring-[#5D48E5]"
                      disabled={isUploading}
                      value={pastedText}
                      onChange={(e) => setPastedText(e.target.value)}
                    ></textarea>
                  </div>
                  <Button
                    type="submit"
                    className="bg-[#5D48E5] hover:bg-[#4938B8] text-white rounded-full px-8 py-2 font-medium"
                    disabled={isUploading}
                  >
                    {isUploading ? (
                      <span className="flex items-center">
                        <Spinner size="sm" className="mr-2 text-white" />
                        Processing...
                      </span>
                    ) : (
                      "Start processing"
                    )}
                  </Button>
                </div>
              </form>
            )}
          </div>
        </div>
      </div>
      </>
  )
}
