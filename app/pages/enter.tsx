import { useEffect, useState } from "react";
import type { Route } from "./+types/enter";
import type { ThirdwebClient } from "thirdweb";
import { thirdWebClient } from "~/thirdweb.client";
import { ConnectEmbed, useActiveAccount, useProfiles } from "thirdweb/react";
import { polygonAmoy } from "thirdweb/chains";
import type { LoginPayload, VerifyLoginPayloadParams } from "thirdweb/auth";
import { inAppWallet } from "thirdweb/wallets";
import { useNavigate } from "react-router";
import Logo from "~/components/shared/Logo";

export function meta({}: Route.MetaArgs) {
  return [
    { title: "readables.ai" },
    { name: "description", content: "Sign in/up to get started." },
  ];
}

export default function Enter() {
  const [client, setClient] = useState<ThirdwebClient|null>(null);
  const navigate = useNavigate();

  const wallets = [
    inAppWallet({
      auth: {
        options: [
          "google",
          "discord",
          "telegram",
          "email",
          "facebook",
          "apple",
          "github",
          "x",
          "passkey",
        ],
      },
    }),
  ];

  // Wait for the thirdWeb Client to be available
  useEffect(() => {
      setClient(thirdWebClient);
  }, [thirdWebClient]);

  return (
    <div className="fixed min-h-dvh flex w-full bg-signup-pattern overflow-visible">
      <div className="w-1/2 overflow-visible bg-transparent">
        {/* Logo in the top left corner */}
        <div className="p-8">
          <Logo variant="light" size="md" />
        </div>

        <div className="h-full flex items-stretch p-8">
          <div className="max-w-md text-white">
            <h1 className="text-3xl font-bold pt-24">
              Sign in to your<br />readables account
            </h1>
            <p className="mt-6 text-md text-white/90">
              Join thousands of users converting text to speech <br />with our AI-powered platform.
            </p>
          </div>
        </div>
      </div>

      <div className="w-1/2 flex items-center align-middle justify-center pb-48 bg-transparent">
        <div className="">
        {client && <ConnectEmbed
              autoConnect={false}
              wallets={wallets}
              showThirdwebBranding={false}
              showAllWallets={false}
              chain={polygonAmoy}
              theme={"light"}
              client={client}
              auth={{
                isLoggedIn: async (address) => {
                  console.log("checking if logged in!", { address });
                  const ret = await fetch( `/isloggedin`);
                  if( ret.ok ) {
                    setTimeout(() => {
                        navigate("/");
                    }, 1000);
                  }
                  return await ret.json();
                },
                doLogin: async (params: VerifyLoginPayloadParams) => {
                  console.log("logging in!", { params });
                  const response = await fetch(`/login`, {
                        method: "POST",
                        headers: {
                          'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(params),
                    });
                    console.log(response.status)
                    if( response.ok && response.status === 200) {
                        setTimeout(() => {
                            navigate("/");
                        }, 1000);
                        return await response.json();
                    } else {
                        console.log("Login failed");
                        throw new Error("Login failed");
                    }
                  },
                getLoginPayload: async (params: {address: string, chainId: number}): Promise<LoginPayload> => {
                  console.log("getting login payload!", { params });
                  return await fetch( `/login?address=${params.address}&chainId=${params.chainId}`)
                      .then((res) => res.json());
                },
                doLogout: async () => {
                  console.log("logging out!");
                  return await fetch(`/logout`, {
                    method: "POST"
                }).then((res) => res.json());
            },
              }} />}
        </div>
      </div>
    </div>  );
}
