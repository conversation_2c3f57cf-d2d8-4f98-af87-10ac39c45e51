import { createCookieSessionStorage } from "react-router";
import { thirdwebAuth } from "./thirdweb.server";
import { decodeJWT } from "thirdweb/utils";

type SessionData = {
  jwt: string;
};

type SessionFlashData = {
  error: string;
};

const { getSession, commitSession, destroySession } = createCookieSessionStorage<SessionData, SessionFlashData>({
      cookie: {
        name: "__session",
        httpOnly: true,
        path: "/",
        sameSite: "lax",
        secrets: ["734j56vzettergdSDFEGkze58mvg"],
        secure: true,
        maxAge: 60 * 60 * 24 , // 1 day, aligns with the thirdweb default
      },
    }
  );

const getLoggedInUserId = async (cookieHeader: string): Promise<string> => {
    // First check if session is valid
    const isAuthed = await isSessionAuthed(cookieHeader);
    if (!isAuthed) {
        return "";
    }

    const jwt = await getDecodedSessionJWT(cookieHeader);
    if( jwt && jwt.payload && jwt.payload.sub ) {
        return jwt.payload.sub;
    } else {
        return "";
    }
}

const getDecodedSessionJWT = async (cookieHeader: string): Promise<any> => {
    const session = await getSession(cookieHeader);
    if( !session.get("jwt") ) {
        console.log("getDecodedSessionJWT: no token");
        return false;
    }

    const jwt:any = session.get("jwt");

    return decodeJWT(jwt);
}

const isSessionAuthed = async (cookieHeader: string): Promise<boolean> => {
    const session = await getSession(cookieHeader);
    console.log("isSessionAuthed.data: ", session.data);
    if( !session.get("jwt") ) {
        console.log("isSessionAuthed: no token");
        return false;
    }

    const jwt:any = session.get("jwt");

    try {
        const authResult = await thirdwebAuth.verifyJWT({ jwt });
        return authResult.valid;
    } catch (error) {
        console.log("JWT verification error:", error);
        return false;
    }
}

export { getSession, commitSession, destroySession, isSessionAuthed, getDecodedSessionJWT, getLoggedInUserId };
