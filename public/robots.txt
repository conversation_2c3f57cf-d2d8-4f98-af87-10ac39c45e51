# robots.txt for readables.ai
User-agent: *
Allow: /
Allow: /landing
Allow: /enter

# Disallow private routes that require authentication
Disallow: /readables
Disallow: /readables/*
Disallow: /create
Disallow: /login
Disallow: /logout
Disallow: /isloggedin

# Disallow API endpoints
Disallow: /api/
Disallow: /*.json$

# Crawl delay to prevent server overload
Crawl-delay: 10

# Sitemap
Sitemap: https://www.readables.ai/sitemap.xml

# Additional rules for specific bots
User-agent: GPTBot
Disallow: /

User-agent: ChatGPT-User
Disallow: /

User-agent: Google-Extended
Disallow: /
