/* This file is auto-generated by SST. Do not edit. */
/* tslint:disable */
/* eslint-disable */
/* deno-fmt-ignore-file */

declare module "sst" {
  export interface Resource {
    "OpenAiApiKey": {
      "type": "sst.sst.Secret"
      "value": string
    }
    "PASSWORD": {
      "type": "sst.sst.Secret"
      "value": string
    }
    "ReadablesBucket": {
      "name": string
      "type": "sst.aws.Bucket"
    }
    "ReadablesPreprocessor": {
      "name": string
      "type": "sst.aws.Function"
    }
    "ReadablesQueue": {
      "type": "sst.aws.Queue"
      "url": string
    }
    "ReadablesReadables": {
      "name": string
      "type": "sst.aws.Dynamo"
    }
    "ReadablesTextExtractor": {
      "name": string
      "type": "sst.aws.Function"
    }
    "ReadablesUploadBucket": {
      "name": string
      "type": "sst.aws.Bucket"
    }
    "ReadablesUsers": {
      "name": string
      "type": "sst.aws.Dynamo"
    }
    "ThirdwebAdminPrivateKey": {
      "type": "sst.sst.Secret"
      "value": string
    }
    "ThirdwebClientId": {
      "type": "sst.sst.Secret"
      "value": string
    }
    "ThirdwebSecretKey": {
      "type": "sst.sst.Secret"
      "value": string
    }
    "USERNAME": {
      "type": "sst.sst.Secret"
      "value": string
    }
    "readables-ai": {
      "type": "sst.aws.React"
      "url": string
    }
  }
}
/// <reference path="sst-env.d.ts" />

import "sst"
export {}