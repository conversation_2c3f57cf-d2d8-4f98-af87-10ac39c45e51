/// <reference path="./.sst/platform/config.d.ts" />

export default $config({
    app(input) {
      return {
        name: "readables-ai",
        removal: input?.stage === "production" ? "retain" : "remove",
        protect: ["production"].includes(input?.stage),
        home: "aws",
        providers: {
          aws: {
            region: "eu-central-1",
            allowedAccountIds: ["************"],
            profile: "readables-ai",
          }
        },

      };
    },
    async run() {
      const ThirdwebClientId = new sst.Secret("ThirdwebClientId");
      const ThirdwebSecretKey = new sst.Secret("ThirdwebSecretKey");
      const ThirdwebAdminPrivateKey = new sst.Secret("ThirdwebAdminPrivateKey");
      const OpenAiApiKey = new sst.Secret("OpenAiApiKey");
      const username = new sst.Secret("USERNAME");
      const password = new sst.Secret("PASSWORD");

      const basicAuth = $resolve([username.value, password.value]).apply(
          ([username, password]) =>
            Buffer.from(`${username}:${password}`).toString("base64")
        );

      const usersTable = new sst.aws.Dynamo("ReadablesUsers", {
          fields: {
            userId: "string"
          },
          primaryIndex: { hashKey: "userId" }
      });

      const readablesTable = new sst.aws.Dynamo("ReadablesReadables", {
          fields: {
            readableId: "string",
            userId: "string",
          },
          primaryIndex: { hashKey: "readableId", rangeKey: "userId" },
          globalIndexes: {
              byUserId: { hashKey: "userId" }
          }
      });

      const uploadQueue = new sst.aws.Queue("ReadablesQueue", {
          visibilityTimeout: "10 minutes"
      });

      const readablesBucket = new sst.aws.Bucket("ReadablesBucket", {
          access: "public",
      });


      const uploadBucket = new sst.aws.Bucket("ReadablesUploadBucket", {
          access: "public",
      });

      //
      // TextExtractor lambda
      //
      const textExtractorFn = new sst.aws.Function("ReadablesTextExtractor", {
          handler: "functions/src/functions/api.handler",
          runtime: "python3.11",
          timeout: "5 minutes",
          memory: "1024 MB",
          link: [readablesBucket]
      });

      //
      // Preprocessor lambda
      //
      const preProcessorFn = new sst.aws.Function("ReadablesPreprocessor", {
          handler: "app/lambdas/preprocessor.handler",
          timeout: "5 minutes",
          memory: "1024 MB",
          runtime: "nodejs22.x",
          link: [readablesTable, uploadQueue, OpenAiApiKey, readablesBucket, textExtractorFn]
      });
      uploadQueue.subscribe(preProcessorFn.arn);

      uploadBucket.notify({
          notifications: [
            {
              name: "ReadablesUploadSubscriber",
              queue: uploadQueue,
              events: ["s3:ObjectCreated:*"]
            }
          ]
        });


      const stage = $app.stage;
      if (stage === "production") {
        new sst.aws.React("readables-ai", {
          domain: {
            name: "www.readables.ai",
            aliases: ["readables.ai"],
            dns: false,
            cert: "arn:aws:acm:us-east-1:************:certificate/3f17f60a-32b5-4518-a1aa-eef3aebe4fb8"
          },
          link: [ThirdwebClientId, ThirdwebSecretKey, ThirdwebAdminPrivateKey, usersTable, readablesTable, uploadBucket, uploadQueue, preProcessorFn, OpenAiApiKey],
        })
      } else if (stage === "dev") {
        new sst.aws.React("readables-ai", {
          edge: {
              viewerRequest: {
                  injection: $interpolate`
                      if ( !event.request.headers.authorization || event.request.headers.authorization.value !== "Basic ${basicAuth}") {
                          return {
                              statusCode: 401,
                              headers: {
                                  "www-authenticate": { value: "Basic" }
                              }
                          };
                      }`,
              },
          },
          domain: {
              name: "dev.readables.ai",
              dns: false,
              cert: "arn:aws:acm:us-east-1:************:certificate/e9abaa63-3672-4053-a37a-6461a1d906c6"
            },
            link: [ThirdwebClientId, ThirdwebSecretKey, ThirdwebAdminPrivateKey, usersTable, readablesTable, uploadBucket, uploadQueue, preProcessorFn, OpenAiApiKey],
        })
      } else {
        new sst.aws.React("readables-ai", {
          link: [ThirdwebClientId, ThirdwebSecretKey, ThirdwebAdminPrivateKey, usersTable, readablesTable, uploadBucket, uploadQueue, preProcessorFn, OpenAiApiKey],
        })
      }

    },
  });
