# Readables.ai

<div align="center">
  <img src="public/logo_white.svg" alt="Readables.ai Logo" width="150" />
  <p><em>Reading was yesterday</em></p>
</div>

## 🔍 Project Overview

Readables.ai is an innovative platform that converts text content into high-quality AI-narrated audio. The application allows users to:

- Upload documents (PDF, TXT) for audio conversion
- Paste URLs to extract and convert web content
- Directly paste text for conversion
- Send emails to convert email content
- Listen to converted content with an intuitive audio player
- Manage a personal library of converted "readables"

The platform leverages advanced AI technologies to preprocess text, removing unnecessary elements like tables of contents and bibliographies, and splitting content into optimally-sized chunks for natural-sounding narration.

## 🚀 Getting Started

### Prerequisites

- Node.js (v18+)
- npm or yarn
- AWS account (for deployment)
- SST CLI

### Setting Up the Project

1. Clone the repository:

```bash
git clone https://github.com/megahash/readables.ai.git
cd readables.ai
```

2. Install dependencies:

```bash
npm install
```

3. Set up local environment variables:

Create a `.env.local` file in the root directory with the following variables:

```
THIRDWEB_CLIENT_ID=your_thirdweb_client_id
THIRDWEB_SECRET_KEY=your_thirdweb_secret_key
OPENAI_API_KEY=your_openai_api_key
```

4. Start the development server:

```bash
npm run dev
```

or, if you have SST installed globally:

```bash
sst dev
```

Your application will be available at `http://localhost:5173`.

## 🏗️ Development with SST

This project uses [SST (Serverless Stack)](https://sst.dev/) for infrastructure as code and deployment.

### Local Development

SST provides a local development environment that simulates AWS services:

```bash
npx sst dev
```

This command starts a local development environment that connects to your AWS account but executes your Lambda functions locally.

### SST Resources

The project uses several SST constructs:

- **DynamoDB Tables**: For storing user data and readables
- **S3 Buckets**: For storing uploaded documents and processed audio files
- **Lambda Functions**: For preprocessing documents and handling API requests
- **React Router v7 Site**: For the frontend application

## 🌐 Deployment

### Staging Concept

The project uses three deployment stages:

1. **Personal**: For individual developer testing
   ```bash
   npx sst deploy --stage <your-name>
   ```

2. **Dev**: For team-wide testing and integration
   ```bash
   npx sst deploy --stage dev
   ```

3. **Production**: For the live application
   ```bash
   npx sst deploy --stage production
   ```

### Stage-Specific Configuration

The `sst.config.ts` file contains stage-specific configurations:

- **Production**: Deploys to www.readables.ai with custom domain and certificate
- **Dev**: Deploys to dev.readables.ai with basic authentication
- **Personal**: Deploys to a generated domain for testing

### Deployment Process

1. Build the application:
   ```bash
   npm run build
   ```

2. Deploy to your chosen stage:
   ```bash
   npx sst deploy --stage <stage-name>
   ```

3. To remove deployed resources (except production):
   ```bash
   npx sst remove --stage <stage-name>
   ```

## 🧪 Testing

The project uses Vitest for testing. Test files are located in the `./tests` directory.

### Running Tests

Run all tests:

```bash
npm test
```

Run tests in watch mode:

```bash
npm run test:watch
```

### Test Structure

- **Unit Tests**: Test individual functions and components
- **Integration Tests**: Test interactions between components
- **Fixtures**: Test data is stored in `./tests/fixtures`

### Example Test Files

- `preprocessor.test.ts`: Tests for the document preprocessing functionality
- `pdf-preprocessor.test.ts`: Tests for PDF-specific preprocessing
- `pre.test.ts`: Tests for text normalization

## 🧩 Project Structure

```
readables.ai/
├── app/                    # Application code
│   ├── components/         # React components
│   ├── hooks/              # Custom React hooks
│   ├── lambdas/            # AWS Lambda functions
│   │   ├── preprocessor.ts # Document preprocessing
│   │   └── python/         # Python Lambda functions
│   ├── lib/                # Utility functions
│   ├── pages/              # Page components
│   ├── routes/             # Route definitions
│   └── root.tsx            # Root component
├── public/                 # Static assets
├── tests/                  # Test files
│   └── fixtures/           # Test data
├── sst.config.ts           # SST configuration
├── vite.config.ts          # Vite configuration
├── vitest.config.ts        # Vitest configuration
└── tsconfig.json           # TypeScript configuration
```

## 🔧 Key Technologies

- **Frontend**: React, React Router v7, TailwindCSS
- **Backend**: AWS Lambda, DynamoDB, S3
- **Infrastructure**: SST, AWS CloudFormation
- **Authentication**: Thirdweb
- **AI/ML**: OpenAI API for text preprocessing
- **Testing**: Vitest

## 📝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/my-feature`
3. Commit your changes: `git commit -am 'Add my feature'`
4. Push to the branch: `git push origin feature/my-feature`
5. Submit a pull request

## 📄 License

This project is proprietary and confidential. All rights reserved.

---

<div align="center">
  <p>Built with ❤️ by the Readables.ai team</p>
</div>
