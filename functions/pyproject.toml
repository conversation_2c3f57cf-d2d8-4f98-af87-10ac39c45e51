[project]
name = "functions"
version = "0.1.0"
description = ""
authors = [{name = "Readables.ai Team"}]
requires-python = "==3.11.*"
dependencies = [
    "sst",
    "beautifulsoup4",
    "ebooklib",
    "PyMuPDF",
    "pymupdf4llm",
    "boto3",
    "numpy"
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.uv.sources]
sst = { git = "https://github.com/sst/sst.git", branch = "dev", subdirectory = "sdk/python" }
