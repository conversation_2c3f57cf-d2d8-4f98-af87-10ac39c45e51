# Text Extractor Lambda Function

This AWS Lambda function extracts text from various file formats stored in S3 buckets.

## Supported File Types

- TXT (plain text files)
- PDF (using PyMuPDF)
- EPUB (using ebooklib)

## Usage

The lambda function can be invoked with the following event structure:

```json
{
  "bucket": "your-s3-bucket-name",
  "key": "path/to/your/file.pdf",
  "file_type": "pdf",  // Optional, will be inferred from file extension if not provided
  "chunk_size": 1000   // Optional, default is 1000 characters
}
```

### Response Format

The function returns a response with the following structure:

```json
{
  "statusCode": 200,
  "body": {
    "chunks": ["Chunk 1 content...", "Chunk 2 content...", "..."],
    "chunk_count": 42,
    "title": "Document Title",
    "file_type": "pdf",
    "reading_time": {
      "total_chars": 12500,
      "estimated_words": 2500,
      "minutes": 12.5,
      "formatted": "12m 30s"
    },
    "source": {
      "bucket": "your-s3-bucket-name",
      "key": "path/to/your/file.pdf"
    }
  }
}
```

### Error Handling

If an error occurs, the function returns an error response:

```json
{
  "statusCode": 400,  // or 500 for server errors
  "body": {
    "error": "Error message"
  }
}
```

## Text Chunking

The extractor now automatically splits the extracted text into manageable chunks using the `chunk_text` function and returns only these chunks (not the full text). This is useful for:

- Processing large documents in smaller pieces
- Preparing text for AI models with token limits
- Creating more manageable segments for text-to-speech conversion
- Reducing response payload size

The chunking algorithm:
1. Splits text at sentence boundaries (periods)
2. Ensures chunks don't exceed the specified size (default: 1000 characters)
3. Handles very long sentences by splitting them at word boundaries
4. Returns an array of text chunks in the response

You can customize the chunk size by providing the `chunk_size` parameter in your request.

## Title Extraction

The extractor automatically extracts a title from the document content. The title extraction algorithm:

1. Looks for patterns that might indicate a title in the first chunk:
   - Lines ending with a colon
   - Lines in ALL CAPS
   - First few lines of the document
2. If no clear title is found, uses the first sentence if it's short enough
3. For longer first sentences, truncates to the first 10 words
4. Falls back to using the file name if no good title can be extracted

## Reading Time Estimation

The extractor includes an estimated reading time for the extracted text. This is calculated based on:

- Average reading speed: 200 words per minute
- Average word length: 5 characters

The reading time estimate includes:
- `total_chars`: Total number of characters in the text
- `estimated_words`: Estimated number of words (total characters / 5)
- `minutes`: Estimated reading time in minutes
- `formatted`: Human-readable format (e.g., "12m 30s" or "1h 15m 30s")

## Project Structure

This project uses a modern Python project structure with `pyproject.toml` for dependency management and build configuration.

### Key Files

- `extractor.py` - The main Lambda function code
- `pyproject.toml` - Project configuration and dependencies
- `requirements.txt` - Simple dependency list (for backward compatibility)
- `setup.sh` - Script to set up the development environment
- `package.sh` - Script to package the Lambda function for deployment
- `test_lambda.py` - Test script for the Lambda function
- `test_chunking.py` - Test script for the text chunking functionality

### pyproject.toml

The `pyproject.toml` file defines:

- Project metadata (name, version, description)
- Dependencies (runtime and development)
- Build configuration
- Tool configurations (black, isort, mypy, pytest)
- UV-specific settings

## Dependencies

- boto3 (AWS SDK for Python)
- beautifulsoup4 (HTML parsing)
- ebooklib (EPUB parsing)
- PyMuPDF (PDF parsing)
- pymupdf4llm (Enhanced PDF parsing)
- numpy (Required by PyMuPDF)

### Development Dependencies

- pytest - For running tests
- pytest-cov - For test coverage
- black - For code formatting
- isort - For import sorting
- mypy - For type checking

## Local Development

This project now uses `pyproject.toml` for dependency management. For local development, we recommend using `uv` instead of `pip` for Python package management.

### Using the Setup Script

The easiest way to set up your development environment is to use the included setup script:

```bash
# Make the script executable if needed
chmod +x setup.sh

# Run the setup script
./setup.sh
```

This will:
1. Install `uv` if it's not already installed
2. Create a virtual environment
3. Install all dependencies including development tools

### Manual Setup

If you prefer to set up manually:

```bash
# Install uv if you don't have it already
curl -sSf https://install.ultraviolet.rs | sh

# Create a virtual environment and install dependencies
uv venv
source .venv/bin/activate

# Install the project in development mode
uv pip install -e ".[dev]"

# Or, if you prefer to use the requirements.txt file:
uv pip install -r requirements.txt
```

## Testing

Run the included test scripts to verify functionality:

```bash
# Run the chunking test
python3 test_chunking.py

# Run the lambda test
python3 test_lambda.py
```

## Deployment

### Using the Package Script

The easiest way to package the Lambda function is to use the included package script:

```bash
# Make the script executable if needed
chmod +x package.sh

# Run the package script
./package.sh
```

This will:
1. Create a temporary directory
2. Install all dependencies
3. Copy the Lambda function code
4. Create a zip file
5. Clean up temporary files

### Manual Packaging

If you prefer to package manually:

1. Install dependencies using `uv`:
```bash
uv pip install -r requirements.txt -t .
```

2. Package the function:
```bash
zip -r function.zip .
```

3. Deploy to AWS Lambda:
```bash
aws lambda create-function \
  --function-name TextExtractor \
  --runtime python3.11 \
  --handler extractor.lambda_handler \
  --memory-size 512 \
  --timeout 300 \
  --zip-file fileb://function.zip \
  --role arn:aws:iam::YOUR_ACCOUNT_ID:role/lambda-s3-role
```

## Calling from Another Lambda

### Input Parameters

The TextExtractor lambda expects the following input parameters:

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `bucket` | string | Yes | The S3 bucket name where the file is stored |
| `key` | string | Yes | The S3 object key (path to the file) |
| `file_type` | string | No | The file type (txt, pdf, epub). If not provided, it will be inferred from the file extension |
| `chunk_size` | number | No | The maximum size of each text chunk in characters (default: 1000) |

### Output Interface

The lambda returns a response with the following structure:

```typescript
interface TextExtractorResponse {
  statusCode: number;
  body: string; // JSON string that needs to be parsed
}

interface ReadingTimeEstimate {
  total_chars: number;
  estimated_words: number;
  minutes: number;
  formatted: string;
}

interface ParsedResponseBody {
  chunks: string[];
  chunk_count: number;
  title: string;
  file_type: string;
  reading_time: ReadingTimeEstimate;
  source: {
    bucket: string;
    key: string;
  };
}
```

### TypeScript Example

Here's how to call the TextExtractor lambda from a TypeScript lambda:

```typescript
// TypeScript example
import { LambdaClient, InvokeCommand } from '@aws-sdk/client-lambda';

// Define interfaces for the request and response
interface TextExtractorRequest {
  bucket: string;
  key: string;
  file_type?: string;
  chunk_size?: number;
}

interface TextExtractorResponseBody {
  chunks: string[];
  chunk_count: number;
  file_type: string;
  reading_time: {
    total_chars: number;
    estimated_words: number;
    minutes: number;
    formatted: string;
  };
  source: {
    bucket: string;
    key: string;
  };
}

export const handler = async (event: any): Promise<any> => {
  const lambdaClient = new LambdaClient({ region: 'eu-central-1' }); // Use your region

  // Prepare the request payload
  const payload: TextExtractorRequest = {
    bucket: 'your-bucket',
    key: 'your-file.pdf',
    chunk_size: 1500  // Optional: customize chunk size
  };

  try {
    // Invoke the TextExtractor lambda
    const command = new InvokeCommand({
      FunctionName: 'TextExtractor',
      InvocationType: 'RequestResponse',
      Payload: JSON.stringify(payload),
    });

    const response = await lambdaClient.send(command);

    // Parse the response
    if (!response.Payload) {
      throw new Error('Empty response from TextExtractor');
    }

    const responseText = Buffer.from(response.Payload).toString('utf-8');
    const result = JSON.parse(responseText);

    // Parse the body
    const body: TextExtractorResponseBody = JSON.parse(result.body);

    console.log(`Extracted ${body.chunk_count} chunks`);
    console.log(`First chunk: ${body.chunks[0].substring(0, 50)}...`);
    console.log(`Estimated reading time: ${body.reading_time.formatted} (${body.reading_time.estimated_words} words)`);

    // Process the chunks as needed
    const processedChunks = body.chunks.map((chunk, index) => {
      return {
        id: `chunk-${index}`,
        content: chunk,
        length: chunk.length
      };
    });

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: 'Text extraction successful',
        chunks: processedChunks,
        reading_time: body.reading_time,
        source: body.source
      })
    };
  } catch (error) {
    console.error('Error invoking TextExtractor:', error);
    return {
      statusCode: 500,
      body: JSON.stringify({ error: 'Failed to extract text' })
    };
  }
};
```

### Node.js Example

```javascript
// Node.js example
const { LambdaClient, InvokeCommand } = require('@aws-sdk/client-lambda');

exports.handler = async (event) => {
  const lambdaClient = new LambdaClient({ region: 'eu-central-1' });

  const params = {
    FunctionName: 'TextExtractor',
    InvocationType: 'RequestResponse',
    Payload: JSON.stringify({
      bucket: 'your-bucket',
      key: 'your-file.pdf',
      chunk_size: 1500  // Optional: customize chunk size
    })
  };

  try {
    const command = new InvokeCommand(params);
    const response = await lambdaClient.send(command);

    const responseText = Buffer.from(response.Payload).toString('utf-8');
    const result = JSON.parse(responseText);

    // Access the chunks and reading time
    const body = JSON.parse(result.body);
    const chunks = body.chunks;
    const readingTime = body.reading_time;
    console.log(`Extracted ${chunks.length} chunks`);
    console.log(`Estimated reading time: ${readingTime.formatted} (${readingTime.estimated_words} words)`);

    return result;
  } catch (error) {
    console.error('Error invoking TextExtractor:', error);
    throw error;
  }
};
```

### Python Example

```python
# Python example
import boto3
import json

def lambda_handler(event, context):
    lambda_client = boto3.client('lambda')

    payload = {
        'bucket': 'your-bucket',
        'key': 'your-file.pdf',
        'chunk_size': 1500  # Optional: customize chunk size
    }

    response = lambda_client.invoke(
        FunctionName='TextExtractor',
        InvocationType='RequestResponse',
        Payload=json.dumps(payload)
    )

    result = json.loads(response['Payload'].read().decode())

    # Access the chunks and reading time
    body = json.loads(result['body'])
    chunks = body['chunks']
    reading_time = body['reading_time']
    print(f"Extracted {len(chunks)} chunks")
    print(f"Estimated reading time: {reading_time['formatted']} ({reading_time['estimated_words']} words)")

    return result
```
