
# Standard library imports
import os
import warnings
from threading import Event
import re
import json
import boto3
import tempfile
from urllib.parse import unquote_plus

from sst import Resource

# Third-party imports
import numpy as np
from ebooklib import epub, ITEM_DOCUMENT
from bs4 import BeautifulSoup
import pymupdf4llm
import fitz

warnings.filterwarnings("ignore", category=UserWarning, module='ebooklib')
warnings.filterwarnings("ignore", category=FutureWarning, module='ebooklib')

def extract_text_from_epub(epub_file):
    book = epub.read_epub(epub_file)
    full_text = ""
    for item in book.get_items():
        if item.get_type() == ITEM_DOCUMENT:
            soup = BeautifulSoup(item.get_body_content(), "html.parser")
            full_text += soup.get_text()
    return full_text

def chunk_text(text, initial_chunk_size=1000):
    """Split text into chunks at sentence boundaries with dynamic sizing."""
    sentences = text.replace('\n', ' ').split('.')
    chunks = []
    current_chunk = []
    current_size = 0
    chunk_size = initial_chunk_size

    for sentence in sentences:
        if not sentence.strip():
            continue  # Skip empty sentences

        sentence = sentence.strip() + '.'
        sentence_size = len(sentence)

        # If a single sentence is too long, split it into smaller pieces
        if sentence_size > chunk_size:
            words = sentence.split()
            current_piece = []
            current_piece_size = 0

            for word in words:
                word_size = len(word) + 1  # +1 for space
                if current_piece_size + word_size > chunk_size:
                    if current_piece:
                        chunks.append(' '.join(current_piece).strip() + '.')
                    current_piece = [word]
                    current_piece_size = word_size
                else:
                    current_piece.append(word)
                    current_piece_size += word_size

            if current_piece:
                chunks.append(' '.join(current_piece).strip() + '.')
            continue

        # Start new chunk if current one would be too large
        if current_size + sentence_size > chunk_size and current_chunk:
            chunks.append(' '.join(current_chunk))
            current_chunk = []
            current_size = 0

        current_chunk.append(sentence)
        current_size += sentence_size

    if current_chunk:
        chunks.append(' '.join(current_chunk))

    return chunks

def extract_chapters_from_epub(epub_file, debug=False):
    """Extract chapters from epub file using ebooklib's metadata and TOC."""
    if not os.path.exists(epub_file):
        raise FileNotFoundError(f"EPUB file not found: {epub_file}")

    book = epub.read_epub(epub_file)
    chapters = []

    if debug:
        print("\nBook Metadata:")
        for key, value in book.metadata.items():
            print(f"  {key}: {value}")

        print("\nTable of Contents:")
        def print_toc(items, depth=0):
            for item in items:
                indent = "  " * depth
                if isinstance(item, tuple):
                    section_title, section_items = item
                    print(f"{indent}• Section: {section_title}")
                    print_toc(section_items, depth + 1)
                elif isinstance(item, epub.Link):
                    print(f"{indent}• {item.title} -> {item.href}")

        if debug:
            print_toc(book.toc)

    def get_chapter_content(soup, start_id, next_id=None):
        """Extract content between two fragment IDs"""
        content = []
        start_elem = soup.find(id=start_id)

        if not start_elem:
            return ""

        # Skip the heading itself if it's a heading
        if start_elem.name in ['h1', 'h2', 'h3', 'h4']:
            current = start_elem.find_next_sibling()
        else:
            current = start_elem

        while current:
            # Stop if we hit the next chapter
            if next_id and current.get('id') == next_id:
                break
            # Stop if we hit another chapter heading
            if current.name in ['h1', 'h2', 'h3'] and 'chapter' in current.get_text().lower():
                break
            content.append(current.get_text())
            current = current.find_next_sibling()

        return '\n'.join(content).strip()

    def process_toc_items(items, depth=0):
        processed = []
        for i, item in enumerate(items):
            if isinstance(item, tuple):
                section_title, section_items = item
                if debug:
                    print(f"{'  ' * depth}Processing section: {section_title}")
                processed.extend(process_toc_items(section_items, depth + 1))
            elif isinstance(item, epub.Link):
                if debug:
                    print(f"{'  ' * depth}Processing link: {item.title} -> {item.href}")

                # Skip if title suggests it's front matter
                if (item.title.lower() in ['copy', 'copyright', 'title page', 'cover'] or
                    item.title.lower().startswith('by')):
                    continue

                # Extract the file name and fragment from href
                href_parts = item.href.split('#')
                file_name = href_parts[0]
                fragment_id = href_parts[1] if len(href_parts) > 1 else None

                # Find the document
                doc = next((doc for doc in book.get_items_of_type(ITEM_DOCUMENT)
                          if doc.file_name.endswith(file_name)), None)

                if doc:
                    content = doc.get_content().decode('utf-8')
                    soup = BeautifulSoup(content, "html.parser")

                    # If no fragment ID, get whole document content
                    if not fragment_id:
                        text_content = soup.get_text().strip()
                    else:
                        # Get the next fragment ID if available
                        next_item = items[i + 1] if i + 1 < len(items) else None
                        next_fragment = None
                        if isinstance(next_item, epub.Link):
                            next_href_parts = next_item.href.split('#')
                            if next_href_parts[0] == file_name and len(next_href_parts) > 1:
                                next_fragment = next_href_parts[1]

                        # Extract content between fragments
                        text_content = get_chapter_content(soup, fragment_id, next_fragment)

                    if text_content:
                        chapters.append({
                            'title': item.title,
                            'content': text_content,
                            'order': len(processed) + 1
                        })
                        processed.append(item)
                        if debug:
                            print(f"{'  ' * depth}Added chapter: {item.title}")
                            print(f"{'  ' * depth}Content length: {len(text_content)} chars")
                            print(f"{'  ' * depth}Word count: {len(text_content.split())}")
        return processed

    # Process the table of contents
    process_toc_items(book.toc)

    # If no chapters were found through TOC, try processing all documents
    if not chapters:
        if debug:
            print("\nNo chapters found in TOC, processing all documents...")

        # Get all document items sorted by file name
        docs = sorted(
            book.get_items_of_type(ITEM_DOCUMENT),
            key=lambda x: x.file_name
        )

        for doc in docs:
            if debug:
                print(f"Processing document: {doc.file_name}")

            content = doc.get_content().decode('utf-8')
            soup = BeautifulSoup(content, "html.parser")

            # Try to find chapter divisions
            chapter_divs = soup.find_all(['h1', 'h2', 'h3'], class_=lambda x: x and 'chapter' in x.lower())
            if not chapter_divs:
                chapter_divs = soup.find_all(lambda tag: tag.name in ['h1', 'h2', 'h3'] and
                                          ('chapter' in tag.get_text().lower() or
                                           'book' in tag.get_text().lower()))

            if chapter_divs:
                # Process each chapter division
                for i, div in enumerate(chapter_divs):
                    title = div.get_text().strip()

                    # Get content until next chapter heading or end
                    content = ''
                    for tag in div.find_next_siblings():
                        if tag.name in ['h1', 'h2', 'h3'] and (
                            'chapter' in tag.get_text().lower() or
                            'book' in tag.get_text().lower()):
                            break
                        content += tag.get_text() + '\n'

                    if content.strip():
                        chapters.append({
                            'title': title,
                            'content': content.strip(),
                            'order': len(chapters) + 1
                        })
                        if debug:
                            print(f"Added chapter: {title}")
            else:
                # No chapter divisions found, treat whole document as one chapter
                text_content = soup.get_text().strip()
                if text_content:
                    # Try to find a title
                    title_tag = soup.find(['h1', 'h2', 'title'])
                    title = title_tag.get_text().strip() if title_tag else f"Chapter {len(chapters) + 1}"

                    if title.lower() not in ['copy', 'copyright', 'title page', 'cover']:
                        chapters.append({
                            'title': title,
                            'content': text_content,
                            'order': len(chapters) + 1
                        })
                        if debug:
                            print(f"Added chapter: {title}")

    # Print summary if in debug mode
    if debug:
        if chapters:
            print("\nSuccessfully extracted {} chapters:".format(len(chapters)))
            for chapter in chapters:
                print(f"  {chapter['order']}. {chapter['title']}")

            total_words = sum(len(chapter['content'].split()) for chapter in chapters)
            print("\nBook Summary:")
            print(f"Total Chapters: {len(chapters)}")
            print(f"Total Words: {total_words:,}")
            print(f"Total Duration: {total_words / 150:.1f} minutes")

            print("\nDetailed Chapter List:")
            for chapter in chapters:
                word_count = len(chapter['content'].split())
                print(f"  • {chapter['title']}")
                print(f"    Words: {word_count:,}")
                print(f"    Duration: {word_count / 150:.1f} minutes")
        else:
            print("\nWarning: No chapters were extracted!")
            print("\nAvailable documents:")
            for doc in book.get_items_of_type(ITEM_DOCUMENT):
                print(f"  • {doc.file_name}")

    return chapters

class PdfParser:
    """Parser for extracting chapters from PDF files.

    Attempts to extract chapters first from table of contents,
    then falls back to markdown-based extraction if TOC fails.
    """

    def __init__(self, pdf_path: str, debug: bool = False, min_chapter_length: int = 50):
        """Initialize PDF parser.

        Args:
            pdf_path: Path to PDF file
            debug: Enable debug logging
            min_chapter_length: Minimum text length to consider as chapter
        """
        self.pdf_path = pdf_path
        self.chapters = []
        self.debug = debug
        self.min_chapter_length = min_chapter_length

        if not os.path.exists(pdf_path):
            raise FileNotFoundError(f"PDF file not found: {pdf_path}")

    def get_chapters(self):
        """Extract chapters from PDF file.

        Returns:
            List of chapter dictionaries with title, content and order.
        """
        if self.debug:
            print("\nDEBUG: Starting chapter extraction...")
            print(f"DEBUG: PDF file: {self.pdf_path}")
            print(f"DEBUG: Min chapter length: {self.min_chapter_length}")

        # Try TOC extraction first
        if self.get_chapters_from_toc():
            if self.debug:
                print(f"\nDEBUG: Successfully extracted {len(self.chapters)} chapters from TOC")
            return self.chapters

        # Fall back to markdown extraction
        if self.debug:
            print("\nDEBUG: TOC extraction failed, trying markdown conversion...")

        self.chapters = self.get_chapters_from_markdown()

        if self.debug:
            print(f"\nDEBUG: Markdown extraction complete")
            print(f"DEBUG: Found {len(self.chapters)} chapters")

        return self.chapters

    def get_chapters_from_toc(self):
        """Extract chapters using PDF table of contents.

        Returns:
            bool: True if chapters were found, False otherwise
        """
        doc = None
        try:
            doc = fitz.open(self.pdf_path)
            toc = doc.get_toc()

            if not toc:
                if self.debug:
                    print("\nDEBUG: No table of contents found")
                return False

            # Print TOC structure if in debug mode
            if self.debug:
                print("\nTable of Contents:")
                for level, title, page in toc:
                    title = self._clean_title(title)
                    indent = "  " * (level - 1)
                    print(f"{indent}{'•' if level > 1 else '>'} {title} (page {page})")
                print(f"\nDEBUG: Found {len(toc)} TOC entries")

            # Extract all chapters, not just level 1
            seen_pages = set()
            chapter_markers = []

            # First try to get level 1 chapters
            for level, title, page in toc:
                if level == 1:
                    title = self._clean_title(title)
                    # Skip empty titles or titles that start on same page as previous entry
                    if title and page not in seen_pages:
                        chapter_markers.append((title, page))
                        seen_pages.add(page)

            # If no level 1 chapters, try all levels
            if not chapter_markers:
                for level, title, page in toc:
                    title = self._clean_title(title)
                    # Skip empty titles or titles that start on same page as previous entry
                    if title and page not in seen_pages:
                        chapter_markers.append((title, page))
                        seen_pages.add(page)

            if not chapter_markers:
                if self.debug:
                    print("\nDEBUG: No chapters found in TOC")
                return False

            if self.debug:
                print(f"\nDEBUG: Found {len(chapter_markers)} chapters:")
                for title, page in chapter_markers:
                    print(f"DEBUG: • {title} (page {page})")

            # Process each chapter
            for i, (title, start_page) in enumerate(chapter_markers):
                if self.debug:
                    print(f"\nDEBUG: Processing chapter {i+1}/{len(chapter_markers)}")
                    print(f"DEBUG: Title: {title}")
                    print(f"DEBUG: Start page: {start_page}")

                # Get chapter end page
                end_page = (chapter_markers[i + 1][1] - 1
                           if i < len(chapter_markers) - 1
                           else doc.page_count)

                # Extract chapter text
                chapter_text = self._extract_chapter_text(doc, start_page - 1, end_page)

                if len(chapter_text.strip()) > self.min_chapter_length:
                    self.chapters.append({
                        'title': title,
                        'content': chapter_text,
                        'order': i + 1
                    })
                    if self.debug:
                        print(f"DEBUG: Added chapter with {len(chapter_text.split())} words")

            return bool(self.chapters)

        except Exception as e:
            if self.debug:
                print(f"\nDEBUG: Error in TOC extraction: {str(e)}")
            return False

        finally:
            if doc:
                doc.close()

    def get_chapters_from_markdown(self):
        """Extract chapters by converting PDF to markdown.

        Returns:
            List of chapter dictionaries
        """
        chapters = []
        try:
            def progress(current, total):
                if self.debug:
                    print(f"\rConverting page {current}/{total}...", end="", flush=True)

            # Convert PDF to markdown
            md_text = pymupdf4llm.to_markdown(
                self.pdf_path,
                show_progress=True,
                progress_callback=progress
            )

            # Clean up markdown text
            md_text = self._clean_markdown(md_text)

            # Extract chapters
            current_chapter = None
            current_text = []
            chapter_count = 0

            for line in md_text.split('\n'):
                if line.startswith('#'):
                    # Save previous chapter if exists
                    if current_chapter and current_text:
                        chapter_text = ''.join(current_text)
                        if len(chapter_text.strip()) > self.min_chapter_length:
                            chapters.append({
                                'title': current_chapter,
                                'content': chapter_text,
                                'order': chapter_count
                            })

                    # Start new chapter
                    chapter_count += 1
                    current_chapter = f"Chapter {chapter_count}_{line.lstrip('#').strip()}"
                    current_text = []
                else:
                    if current_chapter is not None:
                        current_text.append(line + '\n')

            # Add final chapter
            if current_chapter and current_text:
                chapter_text = ''.join(current_text)
                if len(chapter_text.strip()) > self.min_chapter_length:
                    chapters.append({
                        'title': current_chapter,
                        'content': chapter_text,
                        'order': chapter_count
                    })

            return chapters

        except Exception as e:
            if self.debug:
                print(f"\nDEBUG: Error in markdown extraction: {str(e)}")
            return chapters

    def _clean_title(self, title: str) -> str:
        """Clean up chapter title text."""
        return title.strip().replace('\u200b', ' ')

    def _clean_markdown(self, text: str) -> str:
        """Clean up converted markdown text."""
        # Remove page markers
        text = text.replace('-', '')
        # Remove other unwanted characters
        text = re.sub(r'\s+', ' ', text)
        return text.strip()

    def _extract_chapter_text(self, doc, start_page: int, end_page: int) -> str:
        """Extract text from PDF pages."""
        chapter_text = []
        for page_num in range(start_page, end_page):
            try:
                page = doc[page_num]
                text = page.get_text()
                chapter_text.append(text)
            except Exception as e:
                if self.debug:
                    print(f"\nDEBUG: Error extracting page {page_num}: {str(e)}")
                continue

        return '\n'.join(chapter_text)


def extract_text_from_txt(file_path):
    """Extract text from a txt file.

    Args:
        file_path: Path to the txt file

    Returns:
        The text content as a string
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            return file.read()
    except UnicodeDecodeError:
        # Try with different encoding if UTF-8 fails
        with open(file_path, 'r', encoding='latin-1') as file:
            return file.read()


def extract_text_from_pdf(file_path, debug=False):
    """Extract text from a PDF file.

    Args:
        file_path: Path to the PDF file
        debug: Enable debug logging

    Returns:
        The extracted text as a string
    """
    try:
        # First try using the PdfParser class
        parser = PdfParser(file_path, debug=debug)
        chapters = parser.get_chapters()

        # Combine all chapters into a single text
        full_text = ""
        for chapter in chapters:
            full_text += f"{chapter['title']}\n\n{chapter['content']}\n\n"

        # If we got text, return it
        if full_text.strip():
            return full_text

        # If no text was extracted, fall back to direct extraction
        print("No text extracted using PdfParser, falling back to direct extraction")
        return extract_text_from_pdf_direct(file_path)

    except Exception as e:
        print(f"Error in PdfParser: {str(e)}")
        # Fall back to direct extraction
        return extract_text_from_pdf_direct(file_path)


def extract_text_from_pdf_direct(file_path):
    """Extract text directly from a PDF file using PyMuPDF.

    Args:
        file_path: Path to the PDF file

    Returns:
        The extracted text as a string
    """
    try:
        doc = fitz.open(file_path)
        text = ""
        for page in doc:
            text += page.get_text()
        doc.close()
        return text
    except Exception as e:
        print(f"Error in direct PDF extraction: {str(e)}")
        return ""


def extract_title(text_chunks, file_name=""):
    """Extract a title from the text chunks.

    Args:
        text_chunks: List of text chunks
        file_name: Original file name (optional)

    Returns:
        A string containing the extracted title
    """
    # If there are no chunks, return a default title based on the file name
    if not text_chunks:
        if file_name:
            # Extract file name without extension and path
            base_name = os.path.basename(file_name)
            name_without_ext = os.path.splitext(base_name)[0]
            # Convert snake_case or kebab-case to Title Case
            title = name_without_ext.replace('_', ' ').replace('-', ' ').title()
            return title
        return "Untitled Document"

    # Get the first chunk as it's most likely to contain the title
    first_chunk = text_chunks[0]

    # Look for patterns that might indicate a title
    # 1. Check for lines ending with common title markers
    lines = first_chunk.split('\n')
    for line in lines[:3]:  # Check first few lines
        line = line.strip()
        if line and len(line) < 100:  # Reasonable title length
            # If line ends with a title marker or is all caps, it's likely a title
            if (line.endswith((':',)) or
                line.isupper() or
                all(c.isupper() or not c.isalpha() for c in line)):
                return line

    # 2. If no clear title marker, use the first sentence if it's short enough
    sentences = first_chunk.split('.')
    first_sentence = sentences[0].strip()
    if len(first_sentence) < 100:
        return first_sentence

    # 3. If first sentence is too long, extract first N words
    words = first_sentence.split()
    if len(words) > 10:
        title = ' '.join(words[:10]) + '...'
    else:
        title = ' '.join(words)

    # 4. If we still don't have a good title, use the file name
    if len(title) < 3 and file_name:
        base_name = os.path.basename(file_name)
        name_without_ext = os.path.splitext(base_name)[0]
        title = name_without_ext.replace('_', ' ').replace('-', ' ').title()

    return title


def estimate_reading_time(text_chunks):
    """Estimate the reading time for the given text chunks.

    Args:
        text_chunks: List of text chunks

    Returns:
        Dictionary containing reading time estimates in different formats
    """
    # Constants for reading speed
    WORDS_PER_MINUTE = 200  # Average adult reading speed
    CHARS_PER_WORD = 5      # Average characters per word

    # Calculate total character count
    total_chars = sum(len(chunk) for chunk in text_chunks)

    # Estimate word count
    estimated_words = total_chars / CHARS_PER_WORD

    # Calculate reading time in minutes
    reading_time_minutes = estimated_words / WORDS_PER_MINUTE

    # Convert to hours and minutes
    hours = int(reading_time_minutes / 60)
    minutes = int(reading_time_minutes % 60)
    seconds = int((reading_time_minutes * 60) % 60)

    return {
        "total_chars": total_chars,
        "estimated_words": int(estimated_words),
        "minutes": reading_time_minutes,
        "formatted": f"{hours}h {minutes}m {seconds}s" if hours > 0 else f"{minutes}m {seconds}s"
    }


def download_from_s3(bucket, key, local_path):
    """Download a file from S3 to a local path.

    Args:
        bucket: S3 bucket name
        key: S3 object key
        local_path: Local path to save the file

    Returns:
        The local path where the file was saved
    """
    s3_client = boto3.client('s3')
    s3_client.download_file(bucket, key, local_path)
    return local_path


def handler(event, context):
    """AWS Lambda handler for text extraction.

    Args:
        event: Lambda event containing:
            - bucket: S3 bucket name
            - key: S3 object key
            - file_type: Optional file type (txt, pdf, epub)
            - chunk_size: Optional chunk size (default: 1000)
        context: Lambda context

    Returns:
        Dict containing:
            - statusCode: HTTP status code
            - body: JSON string with extracted text and chunks
    """
    try:
        print(f"Received event: {json.dumps(event)}")

        # Extract parameters from the event
        bucket = event.get('bucket')
        key = event.get('key')
        chunk_size = event.get('chunk_size', 1000)  # Default chunk size of 1000

        if not bucket or not key:
            return {
                'statusCode': 400,
                'body': json.dumps({'error': 'Missing required parameters: bucket and key'})
            }

        # Decode the key (S3 keys are URL-encoded)
        key = unquote_plus(key)

        # Determine file type from key if not provided
        file_type = event.get('file_type')
        if not file_type:
            if key.lower().endswith('.txt'):
                file_type = 'txt'
            elif key.lower().endswith('.pdf'):
                file_type = 'pdf'
            elif key.lower().endswith('.epub'):
                file_type = 'epub'
            else:
                return {
                    'statusCode': 400,
                    'body': json.dumps({'error': f'Unsupported file type: {key}'})
                }

        # Create a temporary file to store the downloaded content
        with tempfile.NamedTemporaryFile(delete=False, suffix=f'.{file_type}') as temp_file:
            local_path = temp_file.name

        try:
            # Download the file from S3
            print(f"Downloading {key} from {bucket} to {local_path}")
            download_from_s3(bucket, key, local_path)

            # Extract text based on file type
            if file_type == 'txt':
                extracted_text = extract_text_from_txt(local_path)
            elif file_type == 'pdf':
                extracted_text = extract_text_from_pdf(local_path, debug=False)
            elif file_type == 'epub':
                extracted_text = extract_text_from_epub(local_path)
            else:
                return {
                    'statusCode': 400,
                    'body': json.dumps({'error': f'Unsupported file type: {file_type}'})
                }

            # Generate chunks from the extracted text
            text_chunks = chunk_text(extracted_text, initial_chunk_size=chunk_size)

            # Estimate reading time
            reading_time = estimate_reading_time(text_chunks)

            # Extract title from the text
            title = extract_title(text_chunks, file_name=key)

            # Return the chunks, reading time estimate, and title
            return {
                'statusCode': 200,
                'body': json.dumps({
                    'chunks': text_chunks,
                    'chunk_count': len(text_chunks),
                    'title': title,
                    'file_type': file_type,
                    'reading_time': reading_time,
                    'source': {
                        'bucket': bucket,
                        'key': key
                    }
                })
            }
        finally:
            # Clean up the temporary file
            if os.path.exists(local_path):
                os.remove(local_path)

    except Exception as e:
        print(f"Error: {str(e)}")
        import traceback
        traceback.print_exc()

        return {
            'statusCode': 500,
            'body': json.dumps({'error': str(e)})
        }
