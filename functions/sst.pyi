# Automatically generated by SST
# pylint: disable=all
from typing import Any

class Resource:
    class App:
        name: str
        stage: str
    class OpenAiApiKey:
        type: str
        value: str
    class PASSWORD:
        type: str
        value: str
    class ReadablesBucket:
        name: str
        type: str
    class ReadablesPreprocessor:
        name: str
        type: str
    class ReadablesQueue:
        type: str
        url: str
    class ReadablesReadables:
        name: str
        type: str
    class ReadablesTextExtractor:
        name: str
        type: str
    class ReadablesUploadBucket:
        name: str
        type: str
    class ReadablesUsers:
        name: str
        type: str
    class ThirdwebAdminPrivateKey:
        type: str
        value: str
    class ThirdwebClientId:
        type: str
        value: str
    class ThirdwebSecretKey:
        type: str
        value: str
    class USERNAME:
        type: str
        value: str
    class readables-ai:
        type: str
        url: str

