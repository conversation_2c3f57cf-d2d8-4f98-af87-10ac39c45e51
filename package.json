{"name": "www.readables.ai", "private": true, "type": "module", "scripts": {"build": "react-router build", "dev": "react-router dev", "start": "react-router-serve ./build/server/index.js", "typecheck": "react-router typegen && tsc", "test": "vitest run", "test:watch": "vitest"}, "dependencies": {"@aws-sdk/client-dynamodb": "^3.788.0", "@aws-sdk/client-lambda": "^3.812.0", "@aws-sdk/client-s3": "^3.799.0", "@aws-sdk/s3-request-presigner": "^3.799.0", "@pinecone-database/pinecone": "^5.1.2", "@radix-ui/react-slot": "^1.2.0", "@react-router/node": "^7.5.1", "@react-router/serve": "^7.5.1", "@types/sanitize-html": "^2.15.0", "cheerio": "^1.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "compromise": "^14.14.4", "dayjs": "^1.11.13", "isbot": "^5.1.17", "kokoro-js": "^1.2.0", "lucide-react": "^0.503.0", "motion": "^12.9.4", "natural": "^8.0.1", "openai": "^4.96.2", "pdf-lib": "^1.17.1", "pdf-text-extract": "^1.5.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router": "^7.5.1", "remove-markdown": "^0.6.2", "sanitize-html": "^2.16.0", "sst": "3.13.17", "tailwind-merge": "^3.2.0", "thirdweb": "^5.96.1", "uuid": "^11.1.0"}, "devDependencies": {"@react-router/dev": "^7.5.1", "@tailwindcss/vite": "^4.1.4", "@types/aws-lambda": "8.10.149", "@types/node": "^20", "@types/react": "^19.0.1", "@types/react-dom": "^19.0.1", "@types/uuid": "^10.0.0", "pdfkit": "^0.17.1", "react-router-devtools": "^1.1.0", "tailwindcss": "^4.1.4", "tw-animate-css": "^1.2.8", "typescript": "^5.7.2", "vite": "^5.4.11", "vite-plugin-devtools-json": "^0.1.0", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.1.2"}}