import { describe, it, expect, vi } from 'vitest';
import { preprocessPdfContent } from '../app/lambdas/preprocessor';

// Mock the fs module
vi.mock('fs', () => ({
  writeFileSync: vi.fn(),
  unlinkSync: vi.fn()
}));

// Mock the os module
vi.mock('os', () => ({
  tmpdir: () => '/tmp'
}));

// Mock the path module
vi.mock('path', () => ({
  join: (...args: string[]) => args.join('/')
}));

// Mock the uuid module
vi.mock('uuid', () => ({
  v4: () => 'mock-uuid'
}));

// Mock the pdf-lib module
vi.mock('pdf-lib', () => ({
  PDFDocument: {
    load: vi.fn().mockResolvedValue({
      getPageCount: () => 3
    })
  }
}));

// Mock the pdf-text-extract module
vi.mock('pdf-text-extract', () => {
  const mockExtract = (filePath: string, options: any, callback: Function) => {
    // Simulate successful extraction
    const pages = [
      'Sample PDF Document for Testing\n\nThis is a sample PDF document created for testing the PDF text extraction functionality.',
      'Chapter 1: Introduction\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Vivamus at magna non dui fringilla rhoncus.',
      'Chapter 2: Methods\n\nFusce ac turpis quis ligula lacinia aliquet. Mauris ipsum. Nulla metus metus, ullamcorper vel.'
    ];
    callback(null, pages);
  };

  mockExtract.extractBuffer = (buffer: Buffer, options: any, callback: Function) => {
    // Simulate successful extraction
    const pages = [
      'Sample PDF Document for Testing\n\nThis is a sample PDF document created for testing the PDF text extraction functionality.',
      'Chapter 1: Introduction\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Vivamus at magna non dui fringilla rhoncus.',
      'Chapter 2: Methods\n\nFusce ac turpis quis ligula lacinia aliquet. Mauris ipsum. Nulla metus metus, ullamcorper vel.'
    ];
    callback(null, pages);
  };

  return { default: mockExtract };
});

// Mock the OpenAI API calls
vi.mock('openai', () => {
  return {
    OpenAI: vi.fn().mockImplementation(() => {
      return {
        chat: {
          completions: {
            create: vi.fn().mockResolvedValue({
              choices: [
                {
                  message: {
                    content: JSON.stringify({
                      title: 'Test PDF Document',
                      tags: ['test', 'pdf', 'document']
                    })
                  }
                }
              ]
            })
          }
        }
      };
    })
  };
});

// Mock the Resource import from SST
vi.mock('sst', () => {
  return {
    Resource: {
      OpenAiApiKey: { value: 'test-api-key' }
    }
  };
});

describe('PDF Preprocessor', () => {
  it('should extract text from a PDF file', async () => {
    // Create a dummy buffer - content doesn't matter since we're mocking pdf-parse
    const dummyPdfBuffer = Buffer.from('dummy pdf content');

    // Process the PDF
    const result = await preprocessPdfContent(dummyPdfBuffer);

    // Verify the result
    expect(result).toBeDefined();
    expect(result.normalized).toBeDefined();
    expect(result.title).toBe('Test PDF Document');
    expect(result.tags).toContain('test');
    expect(result.tags).toContain('document');

    // The normalized text should not be empty
    expect(result.normalized.length).toBeGreaterThan(0);
  });
});
