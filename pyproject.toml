[project]
name = "readablesai"
version = "0.1.0"
description = ""
authors = [{name = "Readables.ai Team"}]
dependencies = []

# It is recommended to specify your python version to match your Lambda runtime otherwise you may
# encounter issues with dependencies.
requires-python = "==3.11.*"

[tool.uv.workspace]
members = ["functions"]

[tool.uv.sources]
sst = { git = "https://github.com/sst/sst.git", subdirectory = "sdk/python", branch = "dev" }
