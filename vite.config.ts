import { reactRouter } from "@react-router/dev/vite";
import tailwindcss from "@tailwindcss/vite";
import { defineConfig } from "vite";
import devtoolsJson from 'vite-plugin-devtools-json';
import tsconfigPaths from "vite-tsconfig-paths";
import path from "path";

export default defineConfig({
    server: {
        host: "0.0.0.0",
      },
    plugins: [devtoolsJson(), tailwindcss(), reactRouter(), tsconfigPaths()],
    resolve: {
        alias: {
            "@": path.resolve(__dirname, "./app"),
        },
    },
});
